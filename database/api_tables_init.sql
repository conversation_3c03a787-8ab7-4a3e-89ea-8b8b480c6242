-- API相关数据库表初始化脚本

-- API请求日志表
CREATE TABLE IF NOT EXISTS `wm_api_logs`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `endpoint`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API端点',
    `method`      varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求方法',
    `ip`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求IP',
    `user_agent`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户代理',
    `params`      longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
    `response`    longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应内容',
    `status_code` int(11) NULL DEFAULT NULL COMMENT '状态码',
    `duration`    decimal(10, 2) NULL DEFAULT NULL COMMENT '请求耗时(毫秒)',
    `created_at`  datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_endpoint` (`endpoint`) USING BTREE,
    INDEX `idx_created_at` (`created_at`) USING BTREE,
    INDEX `idx_status_code` (`status_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API请求日志表' ROW_FORMAT = Dynamic;

-- 注意：以下表结构已在主数据库脚本中定义，这里仅作为参考
-- 如果表已存在，请跳过创建步骤

-- 用户主表（使用提供的表结构）
-- CREATE TABLE IF NOT EXISTS `wm_wechat_users` ...

-- 用户身份表（使用提供的表结构）
-- CREATE TABLE IF NOT EXISTS `wm_wechat_identities` ...

-- 消息发送日志表（使用提供的表结构）
-- CREATE TABLE IF NOT EXISTS `wm_message_logs` ...

-- 文件管理表（使用提供的表结构）
-- CREATE TABLE IF NOT EXISTS `wm_files` ...

-- 系统配置表（使用提供的表结构）
-- CREATE TABLE IF NOT EXISTS `wm_settings` ...

-- 更新系统设置表，添加API Token配置
INSERT IGNORE INTO `wm_settings` (`key`, `value`, `name`, `updated_at`) VALUES
('api_token', '', 'API访问令牌', NOW()),
('wechat_open_app_id', '', '微信开放平台AppID', NOW()),
('wechat_open_app_secret', '', '微信开放平台AppSecret', NOW()),
('wechat_mp_app_id', '', '微信公众号AppID', NOW()),
('wechat_mp_app_secret', '', '微信公众号AppSecret', NOW()),
('sms_access_key', '', '短信服务AccessKey', NOW()),
('sms_secret_key', '', '短信服务SecretKey', NOW()),
('s3_endpoint', '', 'S3服务端点', NOW()),
('s3_region', '', 'S3区域', NOW()),
('s3_access_key', '', 'S3 Access Key', NOW()),
('s3_secret_key', '', 'S3 Secret Key', NOW()),
('s3_bucket', '', 'S3存储桶', NOW()),
('s3_cdn_domain', '', 'S3 CDN域名', NOW()),
('system_name', '中科生活消息平台', '系统名称', NOW());
