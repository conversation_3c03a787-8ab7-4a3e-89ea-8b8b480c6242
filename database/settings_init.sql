-- 系统设置表初始化脚本
-- 如果表不存在则创建

CREATE TABLE IF NOT EXISTS `wm_settings`
(
    `key`        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
    `value`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值',
    `name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置项名称 (用于后台显示)',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- 插入默认系统配置（如果不存在）
INSERT IGNORE INTO `wm_settings` (`key`, `value`, `name`, `updated_at`) VALUES
('wechat_app_id', '', '微信小程序AppID', NOW()),
('wechat_app_secret', '', '微信小程序AppSecret', NOW()),
('sms_access_key', '', '短信服务AccessKey', NOW()),
('sms_secret_key', '', '短信服务SecretKey', NOW()),
('system_name', '中科生活消息平台', '系统名称', NOW());
