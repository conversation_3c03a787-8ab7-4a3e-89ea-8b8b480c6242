# 微信公众号"静默-升级"混合授权方案

## 🎯 概述

我已经根据提供的技术文档，使用EasyWeChat库实现了微信公众号的"静默-升级"混合授权方案。该方案优先尝试静默授权（snsapi_base），如果无法获取UnionID，则引导用户进行升级授权（snsapi_userinfo）。

## 🚀 核心思想

**"优先静默，按需升级"** - 始终先尝试通过snsapi_base获取用户信息，如果成功则直接完成登录。如果失败（用户首次使用），则在需要时引导用户进行snsapi_userinfo授权。

## 📋 技术实现

### 1. 后端接口改进

#### wechatMpUserInfo接口
- **路由**: `POST /api/wechatMpUserInfo`
- **功能**: 实现"静默-升级"混合授权逻辑

**请求参数**:
```json
{
    "code": "微信授权码",
    "state": "自定义状态参数"
}
```

**响应类型**:

1. **静默授权成功** (code: 0):
```json
{
    "code": 0,
    "message": "登录成功"
}
```
**说明**: 登录成功后，用户的unionid会自动保存到session中，前端无需处理用户信息和token。

2. **需要升级授权** (code: 1001):
```json
{
    "code": 1001,
    "message": "需要用户授权以获取完整信息",
    "data": {
        "auth_type": "need_upgrade",
        "openid": "用户OpenID",
        "reason": "首次使用需要授权获取用户信息"
    }
}
```

3. **授权失败** (code: 500):
```json
{
    "code": 500,
    "message": "授权失败",
    "data": {
        "auth_type": "error",
        "error_detail": "错误详情"
    }
}
```

### 2. WeChatService核心方法

#### tryGetUserInfoSilently方法
使用EasyWeChat的OAuth功能实现静默授权尝试：

```php
public function tryGetUserInfoSilently(string $code): array
{
    // 使用EasyWeChat创建应用实例
    $app = new OfficialAccount($config);
    
    try {
        // 尝试通过code获取用户信息
        $oauth = $app->getOAuth();
        $user = $oauth->userFromCode($code);
        
        // 获取用户原始数据
        $userRaw = $user->getRaw();
        
        // 检查是否包含UnionID
        if (isset($userRaw['unionid']) && !empty($userRaw['unionid'])) {
            // 静默授权成功
            return ['success' => true, 'data' => $userArray];
        } else {
            // 需要升级授权
            return ['success' => false, 'error_code' => 48001];
        }
    } catch (\Exception $e) {
        // 处理权限不足错误
        if (strpos($e->getMessage(), '48001') !== false) {
            return ['success' => false, 'error_code' => 48001];
        }
        throw $e;
    }
}
```



### 3. 前端登录页面改进

#### 静默授权流程
```javascript
const wechatH5Login = async (code, spread_spid) => {
    const response = await fetch('/api/wechatMpUserInfo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            code: code,
            state: spread_spid || 'default_state'
        })
    });

    const result = await response.json();
    
    if (result.code === 0) {
        // 静默授权成功，unionid已保存到session
        showSuccess.value = true;
    } else if (result.code === 1001) {
        // 需要升级授权，在前端构建认证授权URL
        if (confirm('首次使用需要授权获取用户信息，是否继续？')) {
            const redirectUri = encodeURIComponent(window.location.href);
            const state = Math.random().toString(36).substring(2, 15);
            const upgradeAuthUrl = `https://www.zkshlm.com/login3.php?param=${redirectUri}&state=${state}`;

            window.location.href = upgradeAuthUrl;
        }
    } else {
        // 授权失败
        alert(result.message || '授权失败，请重试');
    }
};
```

#### 静默授权入口
```javascript
const wechatAuthLogin = () => {
    const redirectUri = encodeURIComponent(window.location.href);
    const state = Math.random().toString(36).substring(2, 15);

    // 使用配置好的静默授权链接 (snsapi_base)
    const authUrl = `https://www.zkshlm.com/login4.php?param=${redirectUri}&state=${state}`;

    console.log('跳转到微信静默授权页面:', authUrl);
    window.location.href = authUrl;
};
```

## 🔄 授权流程图

```
用户访问 → 静默授权(snsapi_base) → 获取code → 后端处理
                                                    ↓
                                            检查UnionID存在？
                                                    ↓
                                    是 → 登录成功 → 业务页面
                                    ↓
                                    否 → 返回升级提示 → 用户确认？
                                                        ↓
                                                是 → 升级授权(snsapi_userinfo)
                                                ↓
                                                获取完整信息 → 登录成功
```

## ✨ 核心优势

### 1. 用户体验优化
- **无感知登录**: 老用户直接静默登录，无需任何操作
- **按需授权**: 新用户只在必要时才需要授权
- **一次授权永久受益**: 授权后后续访问都是静默的

### 2. 技术优势
- **使用EasyWeChat**: 利用成熟的微信SDK，减少自定义HTTP请求
- **错误处理完善**: 详细的错误分类和处理逻辑
- **降级处理**: EasyWeChat失败时自动降级到手动构建URL

### 3. 开发友好
- **清晰的状态码**: 不同场景返回不同的状态码便于前端处理
- **详细的响应信息**: 包含足够的信息供前端做出正确的处理决策
- **兼容性好**: 保持与原有接口的兼容性

## 🔧 配置要求

### 1. 微信公众号配置
- 已认证的服务号
- 配置网页授权域名
- 绑定到微信开放平台（获取UnionID的前提）

### 2. 预配置的授权链接
系统使用预配置的授权链接，无需额外配置：
- **静默授权**: `https://www.zkshlm.com/login4.php` (snsapi_base)
- **认证授权**: `https://www.zkshlm.com/login3.php` (snsapi_userinfo)

### 3. 系统配置
在设置页面配置微信公众号参数：
- App ID
- App Secret

## 📊 使用统计

通过不同的auth_type可以统计：
- **silent**: 静默授权成功的用户（老用户）
- **need_upgrade**: 需要升级授权的用户（新用户）
- **error**: 授权失败的情况

## 🎯 最佳实践

1. **State参数使用**: 利用state参数传递用户原始访问页面，授权完成后跳转回去
2. **Token管理**: 合理设置登录Token的过期时间
3. **错误提示**: 给用户友好的错误提示和操作指引
4. **日志记录**: 记录授权流程的关键步骤，便于问题排查

现在您的微信公众号授权系统已经实现了最佳的用户体验，既保证了老用户的无感知登录，又能在必要时获取新用户的完整授权！🎊
