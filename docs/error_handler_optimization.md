# ErrorHandler 优化 - 符合webman-exception最佳实践

## 🎯 优化目标

基于webman-exception文档的最佳实践，优化自定义异常处理器，提供更好的错误处理、日志记录和通知机制。

## 🚀 核心改进

### 1. 异常通知优化

#### 智能通知过滤
```php
// 不需要通知的HTTP状态码
private const IGNORE_NOTIFY_CODES = [400, 401, 402, 403, 404, 410, 422, 429];

// 只通知真正需要关注的错误
private function shouldNotify(\Throwable $e): bool
{
    // 忽略客户端错误和特定状态码
    if (in_array((int)$this->statusCode, self::IGNORE_NOTIFY_CODES)) {
        return false;
    }
    
    // 只通知服务器错误（5xx）和严重的客户端错误
    return (int)$this->statusCode >= 500 || (int)$this->statusCode === 405;
}
```

#### 结构化通知内容
```php
// 分模块构建通知内容
$sections = [
    $this->buildErrorSummary($errorType, $severity),    // 错误概要
    $this->buildErrorDetails($e),                       // 错误详情
    $this->buildRequestInfo(),                          // 请求信息
    $this->buildEnvironmentInfo()                       // 环境信息
];
```

### 2. 安全性增强

#### 敏感信息过滤
```php
// 敏感参数字段定义
private const SENSITIVE_FIELDS = ['password', 'token', 'secret', 'key', 'authorization', 'api_key'];

// 智能敏感字段检测
private function isSensitiveField(string $fieldName): bool
{
    $fieldLower = strtolower($fieldName);
    
    foreach (self::SENSITIVE_FIELDS as $sensitiveField) {
        if (strpos($fieldLower, $sensitiveField) !== false) {
            return true;
        }
    }
    
    return false;
}
```

#### 参数值安全格式化
```php
private function formatParamValue($value, int $maxLength): string
{
    if (is_array($value)) {
        return '[Array(' . count($value) . ')]';
    }
    
    if (is_object($value)) {
        return '[Object(' . get_class($value) . ')]';
    }
    
    // 截断长字符串，防止日志过大
    if (mb_strlen($valueStr) > $maxLength) {
        return mb_substr($valueStr, 0, $maxLength) . '...';
    }
    
    return $valueStr;
}
```

### 3. 响应体优化

#### 环境感知响应
```php
private function buildResponseBody(): array
{
    $isDebug = config('app.debug', false);
    $isServerError = (int)$this->statusCode >= 500;

    $response = [
        'code' => (int)$this->statusCode,
        'message' => $this->getResponseMessage($isServerError, $isDebug),
        'data' => $this->getResponseData($isDebug),
    ];

    // 生产环境下的服务器错误，添加错误追踪ID
    if ($isServerError && !$isDebug) {
        $response['trace_id'] = $this->generateTraceId();
    }

    return $response;
}
```

#### 用户友好的错误消息
```php
private function getResponseMessage(bool $isServerError, bool $isDebug): string
{
    if ($isServerError && !$isDebug) {
        return '服务器内部错误，请稍后重试';  // 生产环境隐藏具体错误
    }

    return $this->errorMessage ?: '未知错误';  // 开发环境显示详细错误
}
```

### 4. 日志记录增强

#### 结构化日志
```php
private function logError(\Throwable $e): void
{
    $context = [
        'exception_class' => get_class($e),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'status_code' => $this->statusCode,
        'error_message' => $this->errorMessage,
        'request_url' => $this->responseData['request_url'] ?? '',
        'client_ip' => $this->responseData['client_ip'] ?? '',
        'request_method' => $this->responseData['request_method'] ?? '',
        'user_agent' => $this->responseData['user_agent'] ?? '',
    ];

    Log::error("系统异常: {$this->errorMessage}", $context);
}
```

### 5. 通知内容优化

#### 清晰的错误概要
```
🚨 错误概要
类型: RuntimeException
级别: 严重
状态码: 500
```

#### 详细的错误信息
```
📋 错误详情
消息: 数据库连接失败
异常类: PDOException
文件: Database.php
行号: 123
错误代码: 2002
```

#### 完整的请求上下文
```
🌐 请求信息
URL: /api/users/create
IP: *************
方法: POST
UA: Mozilla/5.0 (Windows NT 10.0...)
参数: name=test, email=***, password=***
```

#### 环境和时间信息
```
🔧 环境信息
环境: production
时间: 2024-01-15 14:30:25
```

## ✨ 最佳实践特性

### 1. 错误追踪
- **追踪ID**: 生产环境自动生成错误追踪ID
- **关联日志**: 便于运维人员快速定位问题
- **用户友好**: 用户可提供追踪ID协助排查

### 2. 性能优化
- **智能过滤**: 避免无意义的通知和日志
- **内容截断**: 防止超大参数影响性能
- **异步通知**: 通知发送失败不影响主流程

### 3. 安全保护
- **敏感信息**: 自动识别并隐藏敏感参数
- **信息泄露**: 生产环境隐藏技术细节
- **参数过滤**: 防止恶意参数注入日志

### 4. 可维护性
- **模块化**: 功能分离，易于扩展
- **配置化**: 通过配置控制行为
- **标准化**: 遵循PSR规范和最佳实践

## 🔧 配置建议

### 1. 环境配置
```php
// config/app.php
'debug' => env('APP_DEBUG', false),
'env' => env('APP_ENV', 'production'),
```

### 2. 日志配置
```php
// config/log.php
'default' => env('LOG_CHANNEL', 'daily'),
'channels' => [
    'daily' => [
        'handler' => Monolog\Handler\RotatingFileHandler::class,
        'constructor' => [
            runtime_path() . '/logs/webman.log',
            7, // 保留7天
            \Monolog\Logger::DEBUG,
        ],
    ],
],
```

### 3. 队列配置
```php
// config/redis.php
'default' => [
    'host' => env('REDIS_HOST', '127.0.0.1'),
    'port' => env('REDIS_PORT', 6379),
    'database' => env('REDIS_DB', 0),
],
```

## 📊 监控指标

### 1. 错误统计
- 按状态码分类的错误数量
- 按时间段的错误趋势
- 高频错误路径分析

### 2. 性能指标
- 异常处理响应时间
- 通知发送成功率
- 日志写入性能

### 3. 业务指标
- 用户影响范围
- 错误恢复时间
- 问题解决效率

## 🎯 使用效果

### 开发环境
- **详细错误**: 显示完整的错误信息和堆栈
- **调试友好**: 包含请求参数和上下文
- **快速定位**: 精确的文件和行号信息

### 生产环境
- **用户友好**: 隐藏技术细节，显示友好消息
- **安全保护**: 过滤敏感信息，防止信息泄露
- **运维支持**: 提供追踪ID和结构化日志

### 通知系统
- **智能过滤**: 只通知需要关注的错误
- **内容丰富**: 包含完整的错误上下文
- **格式清晰**: 结构化的通知内容

现在您的ErrorHandler已经完全符合webman-exception的最佳实践，提供了企业级的错误处理能力！🎊
