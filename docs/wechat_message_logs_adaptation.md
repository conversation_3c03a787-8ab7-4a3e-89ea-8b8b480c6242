# 微信消息日志表适配方案

## 🎯 适配目标

根据您提供的`wm_message_logs`表结构，修改代码以正确记录微信模板消息发送日志。

## 📊 数据库表结构

### wm_message_logs 表字段
```sql
CREATE TABLE `wm_message_logs` (
    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `unionid` varchar(128) NOT NULL COMMENT '接收消息的用户 UnionID',
    `type` varchar(20) NOT NULL COMMENT '消息类型 (wechat, sms)',
    `template_id` varchar(255) NULL COMMENT '使用的模板 ID',
    `content` text NULL COMMENT '发送内容的快照 (JSON 格式)',
    `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送状态 (0:待发送, 1:成功, 2:失败)',
    `response` text NULL COMMENT '服务商返回的完整响应信息',
    `created_at` datetime(0) NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_unionid`(`unionid`),
    INDEX `idx_type`(`type`),
    INDEX `idx_status`(`status`)
);
```

## 🚀 代码适配

### 1. recordTemplateMessage 方法适配

#### 核心改进
```php
private function recordTemplateMessage(array $data, array $result): bool
{
    // 1. 获取unionid（通过openid查找）
    $config = $this->getOfficialAccountConfig();
    $identity = Medoo::get('wm_wechat_identities', 'unionid', [
        'openid' => $data['touser'],
        'app_id' => $config['app_id']
    ]);
    
    $unionid = $identity ?: $data['touser']; // 备用方案

    // 2. 提取模板消息内容
    $content = [];
    if (isset($data['data'])) {
        foreach ($data['data'] as $key => $value) {
            if (isset($value['value'])) {
                $content[$key] = $value['value'];
            }
        }
    }

    // 3. 构建消息记录数据（适配wm_message_logs表结构）
    $messageData = [
        'unionid' => $unionid,
        'type' => 'wechat',
        'template_id' => $data['template_id'],
        'content' => json_encode($content, JSON_UNESCAPED_UNICODE),
        'status' => (isset($result['errcode']) && $result['errcode'] === 0) ? 1 : 2,
        'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
        'created_at' => date('Y-m-d H:i:s')
    ];

    // 4. 插入消息记录到wm_message_logs表
    return Medoo::insert('wm_message_logs', $messageData);
}
```

### 2. 字段映射关系

| 原字段 | 新字段 | 说明 |
|--------|--------|------|
| touser | unionid | 通过openid查找对应的unionid |
| - | type | 固定值'wechat' |
| template_id | template_id | 直接映射 |
| template_data | content | 提取value值，简化JSON结构 |
| send_result | response | 直接映射 |
| status(success/failed) | status(1/2) | 字符串转数字状态码 |
| send_time | created_at | 直接映射 |

### 3. 状态码映射

```php
// 原状态（字符串）
'status' => 'success' | 'failed'

// 新状态（数字）
'status' => 1 | 2  // 1:成功, 2:失败
```

### 4. 内容格式优化

#### 原格式（复杂）
```json
{
    "character_string7": {"value": "ORD202501110001"},
    "phrase3": {"value": "待审核"},
    "time6": {"value": "2025-01-11 14:30:25"}
}
```

#### 新格式（简化）
```json
{
    "character_string7": "ORD202501110001",
    "phrase3": "待审核",
    "time6": "2025-01-11 14:30:25"
}
```

## ✨ 核心特性

### 1. UnionID 自动查找
```php
// 通过openid和app_id查找unionid
$identity = Medoo::get('wm_wechat_identities', 'unionid', [
    'openid' => $data['touser'],
    'app_id' => $config['app_id']
]);

// 备用方案：如果找不到unionid，使用openid
$unionid = $identity ?: $data['touser'];
```

### 2. 内容简化处理
```php
// 提取模板字段的value值
$content = [];
foreach ($data['data'] as $key => $value) {
    if (isset($value['value'])) {
        $content[$key] = $value['value'];
    }
}
```

### 3. 状态码转换
```php
// 微信API返回errcode=0表示成功，其他表示失败
'status' => (isset($result['errcode']) && $result['errcode'] === 0) ? 1 : 2
```

## 📋 数据示例

### 1. 输入数据
```php
$data = [
    'touser' => 'ow-9xxNhy14ah2FBJUhZeXqCiJRA',
    'template_id' => 'j_GcYvzUt8BX9New2N630gnKv9xCeO3wJeHlBHHUj_k',
    'url' => 'https://img.zkshlm.com/zksh/error.html',
    'data' => [
        'character_string7' => ['value' => 'ORD202501110001'],
        'phrase3' => ['value' => '待审核'],
        'time6' => ['value' => '2025-01-11 14:30:25']
    ]
];

$result = [
    'errcode' => 0,
    'errmsg' => 'ok',
    'msgid' => '2345678901234567890'
];
```

### 2. 数据库记录
```sql
INSERT INTO wm_message_logs (
    unionid,
    type,
    template_id,
    content,
    status,
    response,
    created_at
) VALUES (
    'unionid_from_identity_table',
    'wechat',
    'j_GcYvzUt8BX9New2N630gnKv9xCeO3wJeHlBHHUj_k',
    '{"character_string7":"ORD202501110001","phrase3":"待审核","time6":"2025-01-11 14:30:25"}',
    1,
    '{"errcode":0,"errmsg":"ok","msgid":"2345678901234567890"}',
    '2025-01-11 14:30:25'
);
```

## 🔧 查询示例

### 1. 查看发送成功的消息
```sql
SELECT * FROM wm_message_logs 
WHERE type = 'wechat' AND status = 1 
ORDER BY created_at DESC;
```

### 2. 查看发送失败的消息
```sql
SELECT * FROM wm_message_logs 
WHERE type = 'wechat' AND status = 2 
ORDER BY created_at DESC;
```

### 3. 查看特定用户的消息
```sql
SELECT * FROM wm_message_logs 
WHERE unionid = 'specific_unionid' 
ORDER BY created_at DESC;
```

### 4. 查看特定模板的发送情况
```sql
SELECT 
    template_id,
    COUNT(*) as total,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed
FROM wm_message_logs 
WHERE type = 'wechat' 
GROUP BY template_id;
```

## 🛡️ 错误处理

### 1. UnionID 查找失败
```php
// 如果无法通过openid找到unionid，使用openid作为备用
$unionid = $identity ?: $data['touser'];
```

### 2. 数据库插入失败
```php
if ($insertResult) {
    \support\Log::info('模板消息记录保存成功');
    return true;
} else {
    \support\Log::error('模板消息记录保存失败', [
        'error' => Medoo::error()
    ]);
    return false;
}
```

### 3. 异常处理
```php
try {
    // 主要逻辑
} catch (\Exception $e) {
    \support\Log::error('记录模板消息异常', [
        'error' => $e->getMessage(),
        'data' => $data
    ]);
    return false;
}
```

## 📈 监控建议

### 1. 发送成功率监控
```sql
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
    ROUND(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM wm_message_logs 
WHERE type = 'wechat' 
GROUP BY DATE(created_at) 
ORDER BY date DESC;
```

### 2. 错误分析
```sql
SELECT 
    JSON_EXTRACT(response, '$.errcode') as errcode,
    JSON_EXTRACT(response, '$.errmsg') as errmsg,
    COUNT(*) as count
FROM wm_message_logs 
WHERE type = 'wechat' AND status = 2 
GROUP BY errcode, errmsg 
ORDER BY count DESC;
```

### 3. 用户活跃度
```sql
SELECT 
    unionid,
    COUNT(*) as message_count,
    MAX(created_at) as last_message_time
FROM wm_message_logs 
WHERE type = 'wechat' 
GROUP BY unionid 
ORDER BY message_count DESC;
```

现在代码已经完全适配您的`wm_message_logs`表结构，能够正确记录微信模板消息发送日志！🎊
