# 文件上传API文档

## 概述

本系统提供了完整的文件上传、管理和下载功能，支持S3存储和文件哈希去重。使用AWS SDK for PHP与S3兼容的存储服务（如MinIO、阿里云OSS等）进行交互。所有API接口都需要通过API Token进行认证。

## 技术栈

- **存储后端**: AWS S3 / MinIO / 其他S3兼容存储
- **SDK**: AWS SDK for PHP v3
- **去重算法**: SHA256文件哈希
- **认证方式**: API Token

## 认证

所有API请求都需要在URL参数中包含`token`参数：
```
GET /api/file/list?token=your_api_token
POST /api/file/upload?token=your_api_token
```

## 接口列表

### 1. 文件上传

**接口地址：** `POST /api/file/upload`

**请求参数：**
- `file` (文件) - 必需，要上传的文件
- `unionid` (字符串) - 可选，用户UnionID
- `token` (字符串) - 必需，API访问令牌（URL参数）

**支持的文件类型：**
- 图片：JPEG, PNG, GIF, WebP
- 文档：PDF, Word, Excel, TXT, CSV
- 视频：MP4, AVI, MOV
- 音频：MP3, WAV, AAC

**文件大小限制：** 50MB

**响应示例：**
```json
{
    "code": 0,
    "message": "文件上传成功",
    "data": {
        "id": 1,
        "url": "https://img.zksh.com/zksh/uploads/2025/01/04/abc123.jpg",
        "file_name": "example.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "is_duplicate": false
    }
}
```

**秒传功能：**
如果文件已存在（基于SHA256哈希值），系统会直接返回已有文件信息，不会重复上传：
```json
{
    "code": 0,
    "message": "文件秒传成功",
    "data": {
        "id": 1,
        "url": "https://img.zksh.com/zksh/uploads/2025/01/04/abc123.jpg",
        "file_name": "example.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "is_duplicate": true
    }
}
```

### 2. 批量上传

**接口地址：** `POST /api/file/batch-upload`

**请求参数：**
- `files[]` (文件数组) - 必需，要上传的多个文件
- `unionid` (字符串) - 可选，用户UnionID
- `token` (字符串) - 必需，API访问令牌（URL参数）

**响应示例：**
```json
{
    "code": 0,
    "message": "批量上传完成，成功: 2，失败: 0",
    "data": {
        "results": [
            {
                "index": 0,
                "success": true,
                "message": "文件上传成功",
                "data": {
                    "id": 1,
                    "url": "https://img.zksh.com/zksh/uploads/2025/01/04/file1.jpg",
                    "file_name": "file1.jpg",
                    "size": 1024000,
                    "mime_type": "image/jpeg",
                    "is_duplicate": false
                }
            },
            {
                "index": 1,
                "success": true,
                "message": "文件秒传成功",
                "data": {
                    "id": 2,
                    "url": "https://img.zksh.com/zksh/uploads/2025/01/04/file2.jpg",
                    "file_name": "file2.jpg",
                    "size": 2048000,
                    "mime_type": "image/jpeg",
                    "is_duplicate": true
                }
            }
        ],
        "summary": {
            "total": 2,
            "success": 2,
            "fail": 0
        }
    }
}
```

### 3. 检查文件是否存在（秒传检查）

**接口地址：** `POST /api/file/check-hash`

**请求参数：**
- `file_hash` (字符串) - 必需，文件的SHA256哈希值
- `token` (字符串) - 必需，API访问令牌（URL参数）

**响应示例（文件存在）：**
```json
{
    "code": 0,
    "message": "文件已存在，可以秒传",
    "data": {
        "exists": true,
        "file": {
            "id": 1,
            "url": "https://img.zksh.com/zksh/uploads/2025/01/04/abc123.jpg",
            "file_name": "example.jpg",
            "size": 1024000,
            "mime_type": "image/jpeg"
        }
    }
}
```

**响应示例（文件不存在）：**
```json
{
    "code": 0,
    "message": "文件不存在，需要上传",
    "data": {
        "exists": false
    }
}
```

### 4. 获取文件列表

**接口地址：** `GET /api/file/list`

**请求参数：**
- `unionid` (字符串) - 必需，用户UnionID
- `page` (整数) - 可选，页码，默认1
- `limit` (整数) - 可选，每页数量，默认20，最大100
- `token` (字符串) - 必需，API访问令牌（URL参数）

**响应示例：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "files": [
            {
                "id": 1,
                "unionid": "user123",
                "file_hash": "abc123...",
                "file_name": "example.jpg",
                "object_name": "uploads/2025/01/04/abc123.jpg",
                "bucket_name": "zksh",
                "url": "https://img.zksh.com/zksh/uploads/2025/01/04/abc123.jpg",
                "size": 1024000,
                "mime_type": "image/jpeg",
                "created_at": "2025-01-04 10:30:00",
                "updated_at": "2025-01-04 10:30:00"
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 20,
        "pages": 1
    }
}
```

### 5. 获取文件信息

**接口地址：** `GET /api/file/info`

**请求参数：**
- `file_id` (整数) - 必需，文件ID
- `token` (字符串) - 必需，API访问令牌（URL参数）

**响应示例：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "unionid": "user123",
        "file_hash": "abc123...",
        "file_name": "example.jpg",
        "object_name": "uploads/2025/01/04/abc123.jpg",
        "bucket_name": "zksh",
        "url": "https://img.zksh.com/zksh/uploads/2025/01/04/abc123.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "created_at": "2025-01-04 10:30:00",
        "updated_at": "2025-01-04 10:30:00"
    }
}
```

### 6. 删除文件

**接口地址：** `POST /api/file/delete`

**请求参数：**
- `file_id` (整数) - 必需，文件ID
- `token` (字符串) - 必需，API访问令牌（URL参数）

**响应示例：**
```json
{
    "code": 0,
    "message": "文件删除成功"
}
```

**注意：** 如果多个记录引用同一个文件（相同哈希值），只有当最后一个记录被删除时，才会从S3存储中删除实际文件。

## 错误码说明

- `code: 0` - 成功
- `code: 1` - 失败，具体错误信息在`message`字段中

## 使用示例

### JavaScript/Ajax 上传示例

```javascript
// 单文件上传
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('unionid', 'user123');

fetch('/api/file/upload?token=your_api_token', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        console.log('上传成功:', data.data.url);
    } else {
        console.error('上传失败:', data.message);
    }
});

// 检查文件是否存在（秒传）
const fileHash = await calculateSHA256(file);
fetch('/api/file/check-hash?token=your_api_token', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        file_hash: fileHash
    })
})
.then(response => response.json())
.then(data => {
    if (data.data.exists) {
        console.log('文件已存在，可以秒传:', data.data.file.url);
    } else {
        console.log('文件不存在，需要上传');
        // 执行实际上传
    }
});
```

### cURL 上传示例

```bash
# 上传文件
curl -X POST \
  'http://your-domain.com/api/file/upload?token=your_api_token' \
  -F 'file=@/path/to/your/file.jpg' \
  -F 'unionid=user123'

# 获取文件列表
curl -X GET \
  'http://your-domain.com/api/file/list?token=your_api_token&unionid=user123&page=1&limit=10'

# 删除文件
curl -X POST \
  'http://your-domain.com/api/file/delete?token=your_api_token' \
  -H 'Content-Type: application/json' \
  -d '{"file_id": 1}'
```

## 安装要求

### 1. 依赖安装

确保已安装AWS SDK for PHP：

```bash
composer require aws/aws-sdk-php
```

或者运行：

```bash
composer install
```

### 2. 配置要求

在使用文件上传功能之前，需要在系统设置中配置以下S3参数：

1. **s3_endpoint** - S3服务端点URL (例如: https://s3.amazonaws.com 或 https://minio.example.com)
2. **s3_region** - S3区域 (例如: us-east-1 或 cn-north-1)
3. **s3_access_key** - S3访问密钥ID
4. **s3_secret_key** - S3秘密访问密钥
5. **s3_bucket** - S3存储桶名称
6. **s3_cdn_domain** - 可选，CDN域名用于加速文件访问

### 3. MinIO配置示例

如果使用MinIO，配置示例：

```
s3_endpoint: https://minio.example.com
s3_region: us-east-1
s3_access_key: minioadmin
s3_secret_key: minioadmin
s3_bucket: uploads
s3_cdn_domain: https://cdn.example.com
```

## 特性说明

### 1. 哈希去重
- 系统使用SHA256算法计算文件哈希值
- 相同内容的文件只会在S3中存储一份
- 支持秒传功能，提高上传效率

### 2. 安全性
- 所有接口都需要API Token认证
- 支持文件类型白名单限制
- 文件大小限制保护

### 3. 性能优化
- 支持批量上传
- 配置缓存减少数据库查询
- CDN支持加速文件访问

### 4. 存储管理
- 智能删除：只有当没有其他记录引用时才删除S3文件
- 完整的文件元数据记录
- 支持用户文件隔离
