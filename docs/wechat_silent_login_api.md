# 微信公众号静默授权登录API文档

## 概述

新增了三个微信公众号静默授权相关的API接口，支持通过静默授权获取用户信息的完整流程。

## 接口列表

### 1. 静默授权获取openid

**接口地址**: `POST /api/wechatMpGetOpenid`

**功能说明**: 通过微信授权code获取用户的openid和access_token信息

**请求参数**:
```json
{
    "code": "微信授权返回的code"
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "获取openid成功",
    "data": {
        "openid": "用户的openid",
        "access_token": "用户的access_token",
        "refresh_token": "刷新token",
        "expires_in": 7200,
        "scope": "snsapi_base"
    }
}
```

### 2. 通过openid获取用户详细信息

**接口地址**: `POST /api/wechatMpGetUserByOpenid`

**功能说明**: 通过用户的openid获取用户的详细信息（需要用户已关注公众号）

**请求参数**:
```json
{
    "openid": "用户的openid"
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "获取用户信息成功",
    "data": {
        "openid": "用户的openid",
        "nickname": "用户昵称",
        "headimgurl": "头像URL",
        "sex": 1,
        "province": "省份",
        "city": "城市",
        "country": "国家",
        "unionid": "unionid",
        "subscribe": 1,
        "subscribe_time": 1234567890,
        "language": "zh_CN",
        "remark": "备注",
        "groupid": 0,
        "tagid_list": []
    }
}
```

### 3. 静默授权登录（推荐使用）

**接口地址**: `POST /api/wechatMpSilentLogin`

**功能说明**: 一步完成静默授权登录，自动获取openid并尝试获取用户详细信息

**请求参数**:
```json
{
    "code": "微信授权返回的code"
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "静默授权登录成功",
    "data": {
        "openid": "用户的openid",
        "user_info": {
            "openid": "用户的openid",
            "nickname": "用户昵称",
            "headimgurl": "头像URL",
            "sex": 1,
            "province": "省份",
            "city": "城市",
            "country": "国家",
            "unionid": "unionid",
            "subscribe": 1,
            "subscribe_time": 1234567890,
            "language": "zh_CN",
            "remark": "备注",
            "groupid": 0,
            "tagid_list": []
        },
        "token_info": {
            "openid": "用户的openid",
            "access_token": "用户的access_token",
            "refresh_token": "刷新token",
            "expires_in": 7200,
            "scope": "snsapi_base"
        }
    }
}
```

**注意**: 如果用户未关注公众号，获取用户详情可能失败，但仍会返回openid信息：
```json
{
    "code": 0,
    "message": "静默授权成功，但获取用户详情失败",
    "data": {
        "openid": "用户的openid",
        "user_info": null,
        "error": "获取用户信息失败的原因"
    }
}
```

## 使用场景

### 场景1: 简单静默授权
如果只需要获取用户的openid用于身份识别，使用 `/api/wechatMpGetOpenid`

### 场景2: 获取完整用户信息
如果需要获取用户的详细信息（昵称、头像等），使用 `/api/wechatMpSilentLogin`（推荐）

### 场景3: 分步处理
先调用 `/api/wechatMpGetOpenid` 获取openid，再根据需要调用 `/api/wechatMpGetUserByOpenid` 获取详情

## 错误处理

所有接口都会返回统一的错误格式：
```json
{
    "code": 400,  // 错误码，非0表示失败
    "message": "错误描述",
    "data": null
}
```

常见错误：
- `code: 400` - 参数错误或微信API调用失败
- `code: 500` - 服务器内部错误

## 前端集成示例

```javascript
// 静默授权登录
async function silentLogin(code) {
    try {
        const response = await axios.post('/api/wechatMpSilentLogin', {
            code: code
        });
        
        if (response.data.code === 0) {
            const { openid, user_info } = response.data.data;
            console.log('用户openid:', openid);
            if (user_info) {
                console.log('用户信息:', user_info);
            }
            return response.data.data;
        } else {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('静默授权失败:', error);
        throw error;
    }
}
```

## 数据存储

用户信息会自动保存到以下数据表：
- `wm_wechat_users` - 主用户表（按unionid去重）
- `wm_wechat_identities` - 身份关联表（openid与app_id的关联）
