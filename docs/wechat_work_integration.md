# 企业微信功能集成文档

## 🎯 概述

我已经成功为您的系统集成了企业微信功能，基于EasyWeChat库实现了发送应用消息和查询成员两个核心功能。

## 🚀 新增功能

### 1. 设置页面企业微信配置
- **企业ID (CorpID)**: 企业微信管理后台 -> 我的企业 -> 企业信息
- **应用Secret**: 企业微信管理后台 -> 应用管理 -> 自建应用
- **应用ID (AgentID)**: 企业微信管理后台 -> 应用管理 -> 自建应用

### 2. WeChatService新增方法
- `getWorkConfig()` - 获取企业微信配置
- `sendWorkMessage()` - 发送应用消息
- `getWorkMembers()` - 获取成员列表
- `testWorkConfig()` - 测试企业微信配置

### 3. API接口
- `POST /api/wechatWorkSendMessage` - 发送企业微信消息
- `POST /api/wechatWorkGetMembers` - 获取企业微信成员列表

### 4. 设置测试接口
- `POST /setting/testWechatWork` - 测试企业微信配置
- `POST /setting/testWechatWorkMessage` - 测试发送消息
- `POST /setting/testWechatWorkMembers` - 测试获取成员

## 📋 API使用说明

### 发送企业微信消息

**接口地址**: `POST /api/wechatWorkSendMessage`

**请求参数**:
```json
{
    "token": "zksh62576563",
    "touser": "@all",
    "message": "这是一条测试消息",
    "msgtype": "text"
}
```

**参数说明**:
- `token`: 访问令牌（必填）
- `touser`: 接收消息的用户ID，多个用户用|分隔，@all表示全部用户（默认@all）
- `message`: 消息内容（必填）
- `msgtype`: 消息类型，支持text、markdown（默认text）

**响应示例**:
```json
{
    "code": 0,
    "message": "消息发送成功",
    "data": {
        "errcode": 0,
        "errmsg": "ok",
        "msgid": "xxx"
    }
}
```

### 获取企业微信应用成员列表

**接口地址**: `POST /api/wechatWorkGetMembers`

**请求参数**:
```json
{
    "token": "zksh62576563"
}
```

**参数说明**:
- `token`: 访问令牌（必填）

**响应示例**:
```json
{
    "code": 0,
    "message": "获取应用成员列表成功",
    "data": {
        "agent_info": {
            "agentid": 1000001,
            "name": "通知应用",
            "description": "企业内部通知应用"
        },
        "members": [
            {
                "userid": "zhangsan",
                "name": "张三",
                "type": "user",
                "source": "direct"
            }
        ],
        "departments": [
            {
                "department_id": 1,
                "type": "department"
            }
        ],
        "total_users": 1,
        "total_departments": 1
    }
}
```

## 🛠️ 配置步骤

### 1. 企业微信后台配置

#### 创建自建应用
1. 登录企业微信管理后台
2. 进入"应用管理" -> "自建"
3. 创建应用，记录AgentID和Secret

#### 获取企业信息
1. 进入"我的企业" -> "企业信息"
2. 记录企业ID（CorpID）

### 2. 系统配置
1. 访问系统设置页面：`http://**********/setting`
2. 填写企业微信配置信息（企业ID、应用Secret、应用ID）
3. 点击"测试连接"验证配置
4. 点击"测试发送消息"验证消息发送
5. 点击"测试获取应用成员"验证应用可见范围

## 💡 使用示例

### 发送通知消息
```bash
curl -X POST "http://**********/api/wechatWorkSendMessage" \
-H "Content-Type: application/json" \
-d '{
  "token": "zksh62576563",
  "touser": "@all",
  "message": "系统维护通知：今晚22:00-24:00进行系统升级，期间服务可能中断。",
  "msgtype": "text"
}'
```

### 发送Markdown消息
```bash
curl -X POST "http://**********/api/wechatWorkSendMessage" \
-H "Content-Type: application/json" \
-d '{
  "token": "zksh62576563",
  "touser": "zhangsan|lisi",
  "message": "## 服务器告警\n\n**告警时间**: 2025-01-10 15:30:00\n**告警内容**: CPU使用率超过90%\n**处理建议**: 请立即检查服务器状态",
  "msgtype": "markdown"
}'
```

### 获取应用成员
```bash
curl -X POST "http://**********/api/wechatWorkGetMembers" \
-H "Content-Type: application/json" \
-d '{
  "token": "zksh62576563"
}'
```

## 🔧 技术实现

### EasyWeChat集成
使用EasyWeChat 6.x版本的企业微信模块：
```php
use EasyWeChat\Work\Application as WorkApplication;

$app = new WorkApplication([
    'corp_id' => $config['corp_id'],
    'secret' => $config['secret'],
    'agent_id' => $config['agent_id'],
]);
```

### 消息发送实现
```php
$messageData = [
    'touser' => $toUser,
    'msgtype' => $msgType,
    'agentid' => $config['agent_id'],
    'text' => ['content' => $message]
];

$result = $app->getClient()->postJson('cgi-bin/message/send', $messageData);
```

### 成员获取实现
```php
$result = $app->getClient()->get('cgi-bin/user/list', [
    'department_id' => $departmentId,
    'fetch_child' => $fetchChild ? 1 : 0
]);
```

## 🎯 应用场景

### 1. 系统通知
- 服务器告警通知
- 系统维护通知
- 业务异常通知

### 2. 工作流程
- 审批流程通知
- 任务分配通知
- 进度更新通知

### 3. 应用管理
- 查看应用可见范围
- 管理应用权限
- 监控应用使用情况

## 🔒 安全说明

1. **Token验证**: 所有API接口都需要提供正确的token
2. **配置加密**: 敏感配置信息存储时进行加密
3. **权限控制**: 建议根据实际需求限制消息发送权限
4. **日志记录**: 所有操作都有详细的日志记录

## 📈 监控和日志

### 查看企业微信操作日志
```bash
tail -f runtime/logs/default.log | grep "企业微信"
```

### 查看错误日志
```bash
tail -f runtime/logs/error.log | grep "Work"
```

## 🎉 总结

现在您的系统已经完全集成了企业微信功能：

✅ **完整的配置界面** - 可视化配置企业微信参数
✅ **发送应用消息** - 支持文本和Markdown格式
✅ **获取应用成员** - 查看应用可见范围内的成员和部门
✅ **配置测试功能** - 一键测试各项功能
✅ **API接口完整** - 提供完整的REST API
✅ **错误处理完善** - 详细的错误信息和日志
✅ **安全验证** - Token验证和权限控制

您可以立即开始使用这些功能来增强您的企业内部通信和管理能力！
