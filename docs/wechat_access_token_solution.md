# 微信Access Token失效问题解决方案

## 🎯 问题描述

在调用微信JSSDK接口时遇到以下错误：
```
Failed to get jssdk ticket: {"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest, could get access_token by getStableAccessToken, more details at https://mmbizurl.cn/s/JtxxFh33r rid: 6870ae69-6f29dea8-4743ee8d"}
```

这个错误表明access_token已失效或不是最新的，微信建议使用getStableAccessToken接口。

## 🚀 解决方案

### 1. 启用Stable Access Token

根据EasyWeChat 6.x文档和微信官方建议，启用稳定版接口调用凭据：

#### 配置修改
```php
// app/service/WeChatService.php
private function getWeChatConfig(string $keyPrefix): array
{
    // ... 获取配置代码 ...
    
    return [
        'app_id' => $appId ?: '',
        'secret' => $secret ?: '',
        'use_stable_access_token' => true, // 启用稳定版access_token
        'http' => [
            'timeout' => 30.0,
            'retry' => [
                'status_codes' => [429, 500, 502, 503, 504],
                'max_retries' => 3,
                'delay' => 1000,
                'multiplier' => 2
            ]
        ]
    ];
}
```

### 2. Stable Access Token优势

#### 传统access_token问题
- **有效期短**: 7200秒（2小时）
- **频繁刷新**: 需要定时刷新机制
- **并发冲突**: 多服务器环境下容易冲突
- **缓存复杂**: 需要中心化存储

#### Stable Access Token优势
- **更长有效期**: 最长7200秒，但有智能刷新机制
- **自动刷新**: 平台提前5分钟自动更新
- **平滑过渡**: 新旧token有5分钟重叠期
- **减少调用**: 降低获取token的频率

### 3. 双重保障机制

为确保JSSDK正常工作，实现了双重保障机制：

#### 主要方案：EasyWeChat自动处理
```php
try {
    // 使用EasyWeChat 6.x的工具类
    $utils = $app->getUtils();
    $jssdkConfig = $utils->buildJsSdkConfig(
        url: $url,
        jsApiList: $apis,
        openTagList: [],
        debug: $debug
    );
} catch (\Exception $jssdkException) {
    // 如果失败，使用备用方案
}
```

#### 备用方案：手动构建JSSDK配置
```php
// 手动获取access_token
$accessTokenResponse = $client->get('/cgi-bin/token', [
    'query' => [
        'grant_type' => 'client_credential',
        'appid' => $config['app_id'],
        'secret' => $config['secret']
    ]
]);

// 手动获取jsapi_ticket
$ticketResponse = $client->get('/cgi-bin/ticket/getticket', [
    'query' => [
        'access_token' => $accessTokenData['access_token'],
        'type' => 'jsapi'
    ]
]);

// 手动生成签名
$timestamp = time();
$nonceStr = \Illuminate\Support\Str::random(16);
$string = "jsapi_ticket={$ticket}&noncestr={$nonceStr}&timestamp={$timestamp}&url={$url}";
$signature = sha1($string);
```

## 📊 微信Stable Access Token详解

### 1. 接口信息
- **接口地址**: `POST https://api.weixin.qq.com/cgi-bin/stable_token`
- **调用频率**: 1万次/分钟，50万次/天
- **有效期**: 最长7200秒

### 2. 调用模式

#### 普通模式（推荐）
```json
{
    "grant_type": "client_credential",
    "appid": "APPID",
    "secret": "APPSECRET",
    "force_refresh": false
}
```
- access_token有效期内重复调用不会更新
- 平台提前5分钟自动更新
- 新旧token有5分钟重叠期

#### 强制刷新模式（慎用）
```json
{
    "grant_type": "client_credential",
    "appid": "APPID",
    "secret": "APPSECRET",
    "force_refresh": true
}
```
- 立即使上次获取的access_token失效
- 限制每天20次
- 连续使用需间隔至少30秒

### 3. 最佳实践

#### 存储建议
- access_token存储空间至少512字符
- 建立中心化存储使用
- 至少每5分钟发起一次调用

#### 安全建议
- 仅在access_token泄漏时使用强制刷新
- 立即排查泄漏原因
- 必要时重置appsecret

## 🛡️ 错误处理机制

### 1. 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | access_token过期 | 使用Stable Access Token |
| 40002 | 不合法的凭证类型 | 检查grant_type参数 |
| 40013 | 不合法的AppID | 检查AppID正确性 |
| 40125 | 无效的appsecret | 检查AppSecret正确性 |
| 45009 | 超过天级别频率限制 | 调用clear_quota接口恢复 |
| 45011 | API调用太频繁 | 稍候再试 |

### 2. 日志记录
```php
// 成功日志
\support\Log::info('JSSDK配置生成成功', [
    'url' => $url,
    'apis' => $apis,
    'appId' => $config['app_id']
]);

// 警告日志
\support\Log::warning('JSSDK自动生成失败，尝试手动构建', [
    'error' => $jssdkException->getMessage(),
    'url' => $url
]);

// 错误日志
\support\Log::error('获取JSSDK配置失败', [
    'error' => $e->getMessage(),
    'file' => $e->getFile(),
    'line' => $e->getLine()
]);
```

## 🔧 配置验证

### 1. 检查微信配置
```bash
# 验证AppID和AppSecret
curl -X POST https://api.weixin.qq.com/cgi-bin/stable_token \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "client_credential",
    "appid": "your_app_id",
    "secret": "your_app_secret"
  }'
```

### 2. 测试JSSDK接口
```bash
# 测试获取JSSDK配置
curl -X POST http://your-domain/api/getJssdkConfig \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://your-domain.com/test.html",
    "debug": true
  }'
```

## 📈 监控建议

### 1. 关键指标
- **成功率**: JSSDK配置生成成功率
- **响应时间**: 接口调用响应时间
- **错误分布**: 各类错误码的分布情况
- **调用频率**: access_token获取频率

### 2. 告警设置
- JSSDK配置失败率 > 5%
- 接口响应时间 > 10秒
- 连续出现40001错误
- access_token获取失败

### 3. 日常维护
- 定期检查微信配置有效性
- 监控access_token使用情况
- 及时处理异常告警
- 定期更新EasyWeChat版本

## 🎯 总结

通过以下措施解决了access_token失效问题：

1. ✅ **启用Stable Access Token** - 使用微信推荐的稳定版接口
2. ✅ **配置HTTP重试机制** - 增强网络请求的稳定性
3. ✅ **实现双重保障** - 自动生成失败时的手动备用方案
4. ✅ **完善日志记录** - 便于问题排查和监控
5. ✅ **错误处理机制** - 优雅处理各种异常情况

现在微信JSSDK接口具备了更强的稳定性和容错能力，能够有效应对access_token失效等问题！🎊
