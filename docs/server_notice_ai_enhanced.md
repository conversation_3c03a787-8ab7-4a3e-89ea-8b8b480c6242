# Server酱通知队列 - AI增强版

## 概述

我已经完善了Server酱通知队列，集成了AI大模型来智能优化消息内容，并结合消息表进行完整的日志记录和统计。

## 🚀 新功能特性

### 1. AI智能优化
- **自动内容优化**: 使用GPT-4.1-mini模型优化消息标题和内容
- **智能分类**: 自动识别消息类型（alert/warning/info/maintenance）
- **优先级判断**: 根据内容自动设置优先级（high/medium/low）
- **专业化表达**: 将原始消息转换为更专业、清晰的表达

### 2. 完整的消息管理
- **数据库日志**: 所有消息都记录到`wm_message_logs`表
- **状态跟踪**: 实时跟踪消息状态（待发送/成功/失败）
- **统计分析**: 提供详细的发送统计和成功率分析
- **错误处理**: 完善的异常处理和日志记录

### 3. 增强的队列处理
- **异步处理**: 使用Redis队列异步处理消息
- **容错机制**: AI优化失败时自动降级使用原始内容
- **性能优化**: 合理的超时设置和错误重试机制

## 📋 API接口

### 1. 发送通知接口

**接口地址**: `POST /api/sendServerNotice`

**请求参数**:
```json
{
    "title": "服务器告警",
    "content": "CPU使用率超过90%，请及时处理！",
    "server": "web-server-01",
    "service": "nginx",
    "unionid": "system",
    "enable_ai": true
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "通知已发送到队列，正在处理中",
    "data": {
        "queue_data": {
            "title": "服务器告警",
            "content": "CPU使用率超过90%，请及时处理！",
            "server": "web-server-01",
            "service": "nginx",
            "unionid": "system",
            "enable_ai": true,
            "timestamp": "2025-01-10 15:30:00",
            "template_id": "server_alert_20250110"
        },
        "ai_enabled": true
    }
}
```

### 2. 获取统计接口

**接口地址**: `POST /api/getServerNoticeStats`

**响应示例**:
```json
{
    "code": 0,
    "message": "获取统计成功",
    "data": {
        "total": 156,
        "success": 148,
        "failed": 5,
        "pending": 3,
        "today": 23,
        "success_rate": 94.87
    }
}
```

## 🤖 AI优化示例

### 原始消息
```
标题: 服务器告警
内容: CPU使用率超过90%，请及时处理！
```

### AI优化后
```
标题: 🔥 [高优先级] Web服务器CPU资源告警
内容: 
📊 告警详情：
• 服务器：web-server-01
• 监控指标：CPU使用率
• 当前状态：超过安全阈值（90%）
• 影响范围：可能导致服务响应缓慢

🔧 建议操作：
1. 立即检查高CPU占用进程
2. 考虑重启异常服务
3. 必要时进行负载均衡调整

⏰ 告警时间：2025-01-10 15:30:00

---
📊 消息优化：AI智能优化
🔥 优先级：high
📂 分类：alert
```

## 🛠️ 技术实现

### 队列处理流程
1. **接收消息** → 创建日志记录（状态：待发送）
2. **AI优化** → 调用GPT-4.1-mini优化内容
3. **发送消息** → 调用Server酱API
4. **更新状态** → 记录发送结果和响应

### AI提示词设计
```
你是一个专业的系统消息优化助手。你的任务是优化服务器告警和通知消息，使其更加清晰、专业且易于理解。

请根据以下原则优化消息：
1. 标题要简洁明了，突出关键信息
2. 内容要结构化，包含问题描述、影响范围、建议操作
3. 使用专业但易懂的语言
4. 根据消息内容判断优先级（high/medium/low）
5. 分类消息类型（alert/warning/info/maintenance）
```

### 数据库设计
消息记录存储在`wm_message_logs`表中：
- `unionid`: 用户标识
- `type`: 消息类型（server_notice）
- `template_id`: 模板ID
- `content`: 原始内容（JSON格式）
- `status`: 状态（0:待发送, 1:成功, 2:失败）
- `response`: 发送响应结果

## 🎯 使用场景

### 1. 服务器监控告警
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "服务器告警",
  "content": "CPU使用率超过90%，当前95%",
  "server": "web-01",
  "service": "system",
  "enable_ai": true
}'
```

### 2. 应用服务异常
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "服务异常",
  "content": "Nginx服务停止，网站无法访问",
  "server": "web-01",
  "service": "nginx",
  "enable_ai": true
}'
```

### 3. 维护通知
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "系统维护",
  "content": "计划于今晚23:00进行系统更新",
  "enable_ai": true
}'
```

## 🌐 测试页面

访问 `http://10.0.0.247/server-notice-test` 可以使用可视化界面测试功能：

- ✅ 发送通知测试
- 📊 实时统计查看
- 📝 快速模板选择
- 🤖 AI优化开关

## 📈 监控和统计

### 关键指标
- **总消息数**: 累计发送的消息总数
- **成功率**: 发送成功的消息比例
- **今日消息**: 当天发送的消息数量
- **待处理**: 队列中等待处理的消息

### 日志查看
所有消息处理日志都记录在系统日志中：
- 成功日志: `runtime/logs/info.log`
- 错误日志: `runtime/logs/error.log`

## 🔧 配置说明

### AI API配置
在`ServerSend.php`中配置：
```php
private const AI_API_URL = 'https://tbai.xin/v1/chat/completions';
private const AI_API_KEY = 'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO1';
private const AI_MODEL = 'gpt-4.1-mini';
```

### Server酱配置
通知发送地址已配置为：
```
https://notice.xiouxie.com/api/notify/10d0feee-9cad-41d1-9da4-9eb9f58516a2
```

## 🚀 立即开始

1. **发送测试消息**:
   ```bash
   curl -X POST "http://10.0.0.247/api/sendServerNotice" \
   -H "Content-Type: application/json" \
   -d '{"title": "测试消息", "content": "这是一条测试消息"}'
   ```

2. **查看统计**:
   ```bash
   curl -X POST "http://10.0.0.247/api/getServerNoticeStats"
   ```

3. **访问测试页面**:
   ```
   http://10.0.0.247/server-notice-test
   ```

现在您的消息推送系统已经具备了AI智能优化能力，可以自动将简单的告警信息转换为专业、结构化的通知内容！
