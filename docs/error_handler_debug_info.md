# ErrorHandler 调试信息增强

## 🎯 功能概述

根据`config('app.debug')`配置，在调试模式下返回详细的错误信息，包括异常堆栈、请求信息、系统状态等，便于开发调试和问题排查。

## 🚀 核心功能

### 1. 调试模式检测

```php
private function buildResponseBody(): array
{
    $isDebug = config('app.debug', false);
    
    // 基础响应结构
    $response = [
        'code' => (int)$this->statusCode,
        'message' => $this->getResponseMessage($isServerError, $isDebug),
        'data' => $this->getResponseData($isDebug),
    ];

    // 调试模式下添加详细错误信息
    if ($isDebug) {
        $response['debug'] = $this->buildDebugInfo();
    }

    return $response;
}
```

### 2. 详细调试信息

#### 异常信息
```php
'exception' => [
    'class' => 'Exception',                    // 异常类名
    'message' => '具体错误消息',               // 错误消息
    'file' => '/path/to/file.php',            // 错误文件
    'line' => 123,                            // 错误行号
    'code' => 500,                            // 错误代码
    'trace' => [                              // 堆栈跟踪
        [
            'index' => 0,
            'file' => '/path/to/file.php',
            'line' => 123,
            'function' => 'methodName',
            'class' => 'ClassName',
            'type' => '->'
        ]
    ]
]
```

#### 请求信息
```php
'request' => [
    'url' => '/api/endpoint',                 // 请求URL
    'method' => 'POST',                       // 请求方法
    'ip' => '*************',                 // 客户端IP
    'user_agent' => 'Mozilla/5.0...',        // 用户代理
    'parameters' => [                         // 请求参数（已过滤敏感信息）
        'username' => 'test',
        'password' => '***',
        'data' => '[Array(5)]'
    ]
]
```

#### 系统信息
```php
'system' => [
    'php_version' => '8.1.0',                // PHP版本
    'memory_usage' => '12.34 MB',            // 内存使用量
    'memory_peak' => '15.67 MB',             // 内存峰值
    'execution_time' => '123.456 ms'         // 执行时间
]
```

## ✨ 安全特性

### 1. 敏感信息过滤
```php
private function formatDebugParams(array $params): array
{
    $formatted = [];
    foreach ($params as $key => $value) {
        if ($this->isSensitiveField($key)) {
            $formatted[$key] = '***';  // 隐藏敏感信息
        } else {
            $formatted[$key] = $this->formatParamValue($value, 100);
        }
    }
    return $formatted;
}
```

### 2. 堆栈跟踪限制
```php
private function formatStackTrace(array $trace): array
{
    $maxItems = 10; // 限制显示的堆栈层数
    return array_slice($trace, 0, $maxItems);
}
```

### 3. 生产环境保护
- 调试信息只在`app.debug = true`时显示
- 生产环境返回简洁的错误信息和追踪ID
- 敏感参数自动过滤

## 📊 响应示例

### 调试模式响应
```json
{
    "code": 500,
    "message": "数据库连接失败",
    "data": [],
    "debug": {
        "error_message": "数据库连接失败",
        "status_code": 500,
        "timestamp": "2024-01-15 14:30:25",
        "exception": {
            "class": "PDOException",
            "message": "SQLSTATE[HY000] [2002] Connection refused",
            "file": "/app/service/Database.php",
            "line": 45,
            "code": 2002,
            "trace": [
                {
                    "index": 0,
                    "file": "/app/service/Database.php",
                    "line": 45,
                    "function": "connect",
                    "class": "PDO",
                    "type": "->"
                }
            ]
        },
        "request": {
            "url": "/api/users/create",
            "method": "POST",
            "ip": "*************",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            "parameters": {
                "name": "test user",
                "email": "<EMAIL>",
                "password": "***"
            }
        },
        "system": {
            "php_version": "8.1.0",
            "memory_usage": "12.34 MB",
            "memory_peak": "15.67 MB",
            "execution_time": "123.456 ms"
        }
    }
}
```

### 生产环境响应
```json
{
    "code": 500,
    "message": "服务器内部错误，请稍后重试",
    "data": [],
    "trace_id": "20240115143025_a1b2c3d4"
}
```

## 🔧 配置说明

### 1. 开启调试模式
```php
// config/app.php
'debug' => env('APP_DEBUG', false),
```

### 2. 环境变量配置
```env
# 开发环境
APP_DEBUG=true

# 生产环境
APP_DEBUG=false
```

### 3. 敏感字段配置
```php
private const SENSITIVE_FIELDS = [
    'password', 'token', 'secret', 'key', 
    'authorization', 'api_key', 'access_token'
];
```

## 📈 性能考虑

### 1. 调试信息构建
- 只在调试模式下构建详细信息
- 限制堆栈跟踪深度（最多10层）
- 参数值长度限制（最多100字符）

### 2. 内存使用
- 使用`memory_get_usage(true)`获取真实内存使用
- 格式化显示，便于阅读
- 包含内存峰值信息

### 3. 执行时间
- 支持多种时间源（WEBMAN_START_TIME、REQUEST_TIME_FLOAT）
- 毫秒级精度显示
- 自动降级处理

## 🎯 使用场景

### 1. 开发调试
- 快速定位错误位置
- 查看完整的调用堆栈
- 分析请求参数和系统状态

### 2. 问题排查
- 详细的异常信息
- 请求上下文信息
- 系统资源使用情况

### 3. 性能分析
- 内存使用监控
- 执行时间分析
- 系统环境信息

## 🛡️ 安全最佳实践

### 1. 环境隔离
- 开发环境开启调试模式
- 测试环境选择性开启
- 生产环境必须关闭

### 2. 信息过滤
- 自动识别敏感字段
- 参数值长度限制
- 堆栈信息精简

### 3. 日志记录
- 详细信息记录到日志
- 用户只看到必要信息
- 追踪ID关联日志

## 📝 维护建议

### 1. 定期检查
- 确认生产环境调试模式关闭
- 检查敏感字段列表完整性
- 监控错误响应大小

### 2. 性能监控
- 调试信息构建时间
- 内存使用情况
- 响应体大小

### 3. 安全审计
- 敏感信息泄露检查
- 错误信息暴露评估
- 日志安全性审查

现在ErrorHandler已经具备了完整的调试信息功能，在开发环境下提供详细的错误诊断信息，在生产环境下保持安全和简洁！🎊
