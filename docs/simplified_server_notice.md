# 简化版企业微信消息推送

## 🎯 概述

我已经简化了ServerSend队列的消息推送逻辑，现在使用企业微信应用推送markdown格式的消息给指定用户YangJinJing。

## 🚀 主要变更

### 1. 推送目标变更
- **旧方式**: Server酱推送到 `https://notice.xiouxie.com/api/notify/...`
- **新方式**: 企业微信应用推送给用户 `YangJinJing`

### 2. 消息格式变更
- **消息类型**: text文本格式
- **字节限制**: 最长不超过2048字节
- **编码要求**: 必须是UTF-8编码

### 3. 参数简化
移除了复杂的参数，只保留核心字段：
- `title` - 消息标题（必填）
- `content` - 消息内容（必填）

### 4. 自动化配置
- ✅ `service` - 服务名称自动从环境变量`APP_NAME`获取
- ✅ `timestamp` - 时间戳自动使用当前时间
- ✅ `target_user` - 固定推送给用户`YangJinJing`
- ✅ `ai_optimization` - AI优化固定开启，自动优化消息内容

### 5. 移除的参数
- ❌ `unionid` - 用户ID
- ❌ `server` - 服务器信息
- ❌ `service` - 服务名称（改为环境变量获取）
- ❌ `enable_ai` - AI开关（改为固定开启）
- ❌ 复杂的AI增强信息
- ❌ 优先级、分类等复杂字段

## 📋 API使用

### 发送企业微信通知

**接口地址**: `POST /api/sendServerNotice`

**请求参数**:
```json
{
    "title": "服务器告警",
    "content": "CPU使用率超过90%，请及时处理！"
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "通知已发送到企业微信队列，正在处理中",
    "data": {
        "queue_data": {
            "title": "服务器告警",
            "content": "CPU使用率超过90%，请及时处理！",
            "template_id": "wechat_work_20250110"
        },
        "ai_enabled": true,
        "target_user": "YangJinJing",
        "message_type": "text",
        "service": "ZK Notice API"
    }
}
```

## 🎨 Text消息格式

### 基本格式
```text
服务器告警

CPU使用率超过90%，请及时处理！

---
发送时间: 2025-01-10 15:30:00
服务: ZK Notice API
```

### 格式特点
- **纯文本格式**: 简洁清晰，兼容性好
- **自动换行**: 支持多行内容显示
- **分割线**: 使用`---`分隔不同部分
- **时间戳**: 自动添加发送时间
- **服务标识**: 显示消息来源服务

## 🔧 内容处理和验证

### 智能长度处理
当text内容超过2048字节时，系统会自动处理：

1. **AI智能精简**: 使用AI服务精简内容，保留核心信息
2. **优雅降级**: AI精简失败时，智能截断并添加提示
3. **长度验证**: 确保最终内容符合企业微信限制

### 内容验证流程
系统会自动验证text内容：

1. **UTF-8编码检查**: 确保内容是有效的UTF-8编码
2. **字节长度检查**: 确保内容不超过2048字节
3. **内容非空检查**: 确保内容不为空

验证失败时会返回相应的错误信息。

### AI精简示例
```
原始内容（3000字节）:
"系统监控报告：CPU使用率95%，内存使用率92%，磁盘使用率88%...（大量详细信息）"

AI精简后（1800字节）:
"系统告警
CPU: 95% 超限
内存: 92% 告警
磁盘: 88% 正常
建议: 立即检查CPU和内存使用情况"
```

## 💡 使用示例

### 1. 基本告警
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "服务器告警",
  "content": "CPU使用率超过90%，当前95%"
}'
```

### 2. 服务异常
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "服务异常",
  "content": "Nginx服务停止，网站无法访问"
}'
```

### 3. 数据库异常
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "数据库连接异常",
  "content": "MySQL连接超时，影响用户登录"
}'
```

### 4. 长内容自动精简
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "系统详细报告",
  "content": "这是一个非常长的系统报告内容...（超过2048字节时会自动使用AI精简）"
}'
```

## 🌐 测试页面

访问测试页面：`http://10.0.0.247/server-notice-test`

测试页面已简化，只包含：
- 通知标题
- 通知内容
- 服务名称（自动从环境变量获取，只读显示）
- AI优化状态（固定开启，显示提示）

## 🔍 队列处理流程

1. **接收消息** → 创建消息日志
2. **AI优化**（可选） → 优化消息内容
3. **构建Markdown** → 格式化为markdown
4. **内容验证** → 验证编码和长度
5. **发送企业微信** → 推送给YangJinJing
6. **更新状态** → 记录发送结果

## 📊 消息日志

所有消息都会记录到`wm_message_logs`表：
- `type`: `server_notice`
- `template_id`: `wechat_work_YYYYMMDD`
- `content`: 原始消息内容（JSON格式）
- `status`: 发送状态（0:待发送, 1:成功, 2:失败）
- `response`: 企业微信API响应

## 🎯 优势

### 简化配置
- ✅ 参数大幅简化，只保留核心字段
- ✅ 默认配置，无需复杂设置
- ✅ 统一推送目标（YangJinJing）

### 企业微信集成
- ✅ 原生企业微信应用推送
- ✅ Markdown格式支持
- ✅ 实时消息通知

### 智能内容处理
- ✅ 自动UTF-8编码验证
- ✅ 智能长度检查和AI精简
- ✅ 优雅的内容截断处理
- ✅ 环境变量自动配置

### 错误处理
- ✅ 完善的错误提示
- ✅ 详细的日志记录
- ✅ 优雅的降级处理

## 🚀 立即使用

现在您可以：

1. **发送简单通知**:
   ```bash
   curl -X POST "http://10.0.0.247/api/sendServerNotice" \
   -H "Content-Type: application/json" \
   -d '{"title": "测试消息", "content": "这是一条测试消息"}'
   ```

2. **访问测试页面**: `http://10.0.0.247/server-notice-test`

3. **查看消息统计**: `http://10.0.0.247/api/getServerNoticeStats`

现在消息推送更加简洁高效，专注于核心功能，通过企业微信直接推送给指定用户！🎊
