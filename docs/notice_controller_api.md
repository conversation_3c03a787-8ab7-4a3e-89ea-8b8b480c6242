# NoticeController 接口文档

## 📋 接口概述

本文档描述了NoticeController中的三个核心接口，分别用于微信开放平台用户信息获取、微信公众号用户信息获取和企业微信消息发送功能。

---

## 🔗 1. 微信开放平台用户信息获取

### 基本信息
- **接口名称**: 微信开放平台code换取微信用户详情
- **请求路径**: `/api/wechatOpenUserInfo`
- **请求方式**: `POST`
- **接口描述**: 通过微信开放平台授权码获取用户详细信息

### 请求参数

#### Headers
```
Content-Type: application/json
```

#### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信开放平台授权码 |

### 响应参数

#### 1. 成功响应 (code: 200)
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "openid": "o1234567890abcdef",
        "unionid": "u1234567890abcdef",
        "nickname": "张三",
        "sex": 1,
        "province": "广东",
        "city": "深圳",
        "country": "中国",
        "headimgurl": "https://thirdwx.qlogo.cn/mmopen/...",
        "privilege": [],
        "language": "zh_CN"
    }
}
```

#### 2. 参数错误响应 (code: 400)
```json
{
    "code": 400,
    "message": "缺少授权码参数"
}
```

#### 3. 微信API错误响应 (code: 500)
```json
{
    "code": 500,
    "message": "获取用户信息失败: invalid code",
    "data": {
        "errcode": 40029,
        "errmsg": "invalid code"
    }
}
```

#### 4. 系统异常响应 (code: 500)
```json
{
    "code": 500,
    "message": "系统异常: Connection timeout"
}
```

---

## 💬 2. 微信公众号用户信息获取

### 基本信息
- **接口名称**: 微信公众号code换取微信用户详情
- **请求路径**: `/api/wechatMpUserInfo`
- **请求方式**: `POST`
- **接口描述**: 通过微信公众号授权码获取用户详细信息（静默授权）

### 请求参数

#### Headers
```
Content-Type: application/json
```

#### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信公众号授权码 |

### 响应参数

#### 1. 成功响应 (code: 200)
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "openid": "ow-9xxNhy14ah2FBJUhZeXqCiJRA",
        "unionid": "u1234567890abcdef",
        "nickname": "李四",
        "sex": 2,
        "province": "北京",
        "city": "北京",
        "country": "中国",
        "headimgurl": "https://thirdwx.qlogo.cn/mmopen/...",
        "privilege": [],
        "language": "zh_CN",
        "subscribe": 1,
        "subscribe_time": 1641888000,
        "remark": "",
        "groupid": 0,
        "tagid_list": []
    }
}
```

#### 2. 参数错误响应 (code: 400)
```json
{
    "code": 400,
    "message": "缺少授权码参数"
}
```

#### 3. 需要用户授权响应 (code: 48001)
```json
{
    "success": false,
    "error_code": 48001,
    "code": 48001,
    "message": "需要用户授权以获取完整信息",
    "openid": "ow-9xxNhy14ah2FBJUhZeXqCiJRA",
    "error_detail": "静默授权无法获取unionid，需要snsapi_userinfo授权"
}
```

#### 5. 微信API错误响应 (code: 500)
```json
{
    "code": 500,
    "message": "获取用户信息失败: access_token expired",
    "data": {
        "errcode": 42001,
        "errmsg": "access_token expired"
    }
}
```

#### 6. 授权码无效响应 (code: 500)
```json
{
    "code": 500,
    "message": "获取用户信息失败: invalid code",
    "data": {
        "errcode": 40029,
        "errmsg": "invalid code"
    }
}
```

#### 7. 网络异常响应 (code: 500)
```json
{
    "code": 500,
    "message": "获取用户信息失败: 网络请求超时",
    "data": []
}
```

#### 8. 系统异常响应 (code: 500)
```json
{
    "code": 500,
    "message": "系统异常: Database connection failed"
}
```

---

## 🏢 3. 企业微信消息发送

### 基本信息
- **接口名称**: 企业微信消息发送
- **请求路径**: `/api/wechatWorkSendMessage`
- **请求方式**: `POST`
- **接口描述**: 将企业微信消息加入发送队列进行异步处理

### 请求参数

#### Headers
```
Content-Type: application/json
```

#### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 否 | 消息标题 |
| content | string | 是 | 消息内容 |

### 响应参数

#### 1. 成功响应 (code: 200)
```json
{
    "code": 200,
    "message": "企业微信消息已加入发送队列",
    "data": {
        "queue_time": "2025-01-11 14:30:25"
    }
}
```

#### 2. 参数错误响应 (code: 400)
```json
{
    "code": 400,
    "message": "消息内容不能为空"
}
```

#### 3. Redis连接失败响应 (code: 500)
```json
{
    "code": 500,
    "message": "发送企业微信消息失败: Redis connection failed"
}
```

#### 4. 队列发送失败响应 (code: 500)
```json
{
    "code": 500,
    "message": "发送企业微信消息失败: Failed to send message to queue"
}
```

#### 5. 系统异常响应 (code: 500)
```json
{
    "code": 500,
    "message": "发送企业微信消息失败: Unexpected error occurred"
}
```

---

## 📊 状态码说明

| 状态码 | 说明 | 适用接口 |
|--------|------|----------|
| 200 | 请求成功 | 所有接口 |
| 400 | 请求参数错误 | 所有接口 |
| 48001 | 需要用户授权 | 微信公众号用户信息获取 |
| 500 | 服务器内部错误 | 所有接口 |

## 🔍 错误类型分类

### 1. 参数验证错误 (400)
- 缺少必填参数
- 参数格式不正确
- 参数值无效

### 2. 授权相关错误 (48001)
- 静默授权无法获取完整用户信息
- 需要用户主动授权snsapi_userinfo权限
- 只能获取openid，无法获取unionid和详细信息

### 3. 微信API错误 (500)
- 授权码无效或过期
- access_token过期
- 微信服务器异常
- API调用频率限制

### 4. 系统错误 (500)
- 数据库连接失败
- Redis连接失败
- 网络超时
- 内存不足
- 未知异常

### 5. 业务逻辑错误 (500)
- 用户不存在
- 权限不足
- 配置错误
- 服务不可用

## 📝 字段说明

### 微信用户信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| openid | string | 用户在当前应用的唯一标识 |
| unionid | string | 用户在开放平台的唯一标识 |
| nickname | string | 用户昵称 |
| sex | int | 性别：1男性，2女性，0未知 |
| province | string | 省份 |
| city | string | 城市 |
| country | string | 国家 |
| headimgurl | string | 头像URL |
| privilege | array | 用户特权信息 |
| language | string | 语言 |
| subscribe | int | 是否关注公众号：1已关注，0未关注 |
| subscribe_time | int | 关注时间戳 |
| remark | string | 公众号运营者对粉丝的备注 |
| groupid | int | 用户所在的分组ID |
| tagid_list | array | 用户被打上的标签ID列表 |

### 企业微信消息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| title | string | 消息标题（可选） |
| content | string | 消息内容（必填） |
| queue_time | string | 加入队列时间 |

## 🛡️ 安全注意事项

### 1. 授权码安全
- 授权码具有时效性，通常10分钟内有效
- 每个授权码只能使用一次
- 确保授权码来源可信

### 2. 用户信息保护
- 用户信息包含敏感数据，需妥善保护
- 遵循相关数据保护法规
- 不得滥用用户信息

### 3. 接口调用安全
- 实施适当的频率限制
- 记录详细的访问日志
- 监控异常调用行为

---

**文档版本**: 1.0.0  
**更新日期**: 2025-01-11  
**维护人员**: 系统管理员
