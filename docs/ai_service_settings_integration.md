# AI服务配置集成到设置页面

## 🎯 概述

我已经成功将AIService的大模型调用配置集成到设置页面，不再需要通过构造函数传入参数。现在所有AI配置都可以通过Web界面进行管理。

## 🚀 主要变更

### 1. 设置页面新增AI配置项

在设置页面添加了AI服务配置区块：

- **AI API地址**: AI服务的API端点地址
- **AI API密钥**: 用于访问AI服务的API密钥
- **AI模型名称**: 使用的AI模型名称
- **请求超时时间**: API请求超时时间（秒）
- **测试AI连接**: 一键测试AI配置是否正确

### 2. AIService类重构

#### 移除构造函数参数
```php
// 旧方式
$aiService = new AIService(
    'https://tbai.xin/v1/chat/completions',
    'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO1',
    'gpt-4.1-mini'
);

// 新方式
$aiService = new AIService(); // 配置自动从设置中获取
```

#### 新增配置获取方法
```php
private function getAIConfig(): array
{
    return [
        'api_url' => $apiUrl ?: 'https://tbai.xin/v1/chat/completions',
        'api_key' => $apiKey ?: 'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO4',
        'model' => $model ?: 'gpt-4.1-mini',
        'timeout' => intval($timeout ?: 30),
    ];
}
```

#### 新增测试方法
```php
public function testAIConfig(string $apiUrl, string $apiKey, string $model, int $timeout = 30): array
```

### 3. 队列服务更新

ServerSend队列的AIService初始化简化：
```php
// 旧方式
$this->aiService = new AIService(
    'https://tbai.xin/v1/chat/completions',
    'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO1',
    'gpt-4.1-mini'
);

// 新方式
$this->aiService = new AIService(); // 配置从设置页面获取
```

### 4. 新增测试接口

在SettingController中添加了AI测试接口：
```php
POST /setting/testAI
```

## 📋 配置说明

### 默认配置值

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| AI API地址 | `https://tbai.xin/v1/chat/completions` | AI服务API端点 |
| AI API密钥 | `sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO4` | 当前有效的API密钥 |
| AI模型名称 | `gpt-4.1-mini` | 使用的AI模型 |
| 请求超时时间 | `30` | API请求超时时间（秒） |

### 配置存储

所有AI配置都存储在`wm_settings`表中：
- `ai_api_url` - AI API地址
- `ai_api_key` - AI API密钥
- `ai_model` - AI模型名称
- `ai_timeout` - 请求超时时间

### 缓存机制

配置使用Redis缓存，缓存时间300秒：
```php
$apiUrl = \app\support\RedisCache::get('ai_api_url', function() {
    return Medoo::get('wm_settings', 'value', ['key' => 'ai_api_url']);
}, 300);
```

## 🛠️ 使用方式

### 1. 配置AI服务

1. 访问设置页面：`http://10.0.0.247/setting`
2. 找到"AI服务设置"区块
3. 填写AI配置信息：
   - AI API地址
   - AI API密钥
   - AI模型名称
   - 请求超时时间
4. 点击"测试AI连接"验证配置
5. 点击"保存设置"保存配置

### 2. 测试AI配置

```bash
curl -X POST "http://10.0.0.247/setting/testAI" \
-H "Content-Type: application/json" \
-d '{
  "api_url": "https://tbai.xin/v1/chat/completions",
  "api_key": "sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO4",
  "model": "gpt-4.1-mini",
  "timeout": 30
}'
```

### 3. 使用AI服务

现在所有地方使用AIService都无需传入参数：

```php
// 在任何地方使用
$aiService = new \app\utils\AIService();

// 生成摘要
$result = $aiService->generateSummary($text, 200);

// 情感分析
$result = $aiService->analyzeSentiment($text);

// 内容分类
$result = $aiService->classifyContent($text);

// 优化服务器告警
$result = $aiService->optimizeServerAlert($title, $content, $context);
```

## 🔧 技术实现

### 配置获取流程

1. **缓存检查**: 首先检查Redis缓存
2. **数据库查询**: 缓存未命中时查询数据库
3. **默认值**: 配置为空时使用默认值
4. **缓存更新**: 将查询结果缓存300秒

### 配置验证

测试AI配置时会：
1. 验证参数完整性
2. 构建测试请求
3. 发送API调用
4. 验证响应格式
5. 返回测试结果

### 错误处理

- 配置缺失时使用默认值
- API调用失败时记录详细错误日志
- 提供友好的错误提示信息

## 🎯 优势

### 1. 统一管理
- 所有AI配置集中在设置页面
- 可视化配置界面
- 实时配置测试

### 2. 灵活性
- 支持动态切换AI服务提供商
- 支持不同模型配置
- 支持超时时间调整

### 3. 维护性
- 无需修改代码即可更换AI配置
- 配置变更立即生效（通过缓存刷新）
- 统一的配置管理方式

### 4. 安全性
- API密钥统一管理
- 支持密码字段隐藏
- 配置测试验证

## 📈 监控和日志

### 查看AI配置
```bash
# 查看当前AI配置
curl -X POST "http://10.0.0.247/setting/get" | grep ai_
```

### 查看AI请求日志
```bash
tail -f runtime/logs/default.log | grep "AI API请求"
```

### 查看AI错误日志
```bash
tail -f runtime/logs/error.log | grep "AI"
```

## 🎉 总结

现在AI服务配置已经完全集成到设置页面：

✅ **可视化配置** - 通过Web界面管理所有AI配置
✅ **实时测试** - 一键测试AI配置是否正确
✅ **统一管理** - 所有AI相关配置集中管理
✅ **动态更新** - 配置变更无需重启服务
✅ **缓存优化** - 配置读取性能优化
✅ **错误处理** - 完善的错误处理和日志记录

您可以立即通过设置页面管理AI服务配置，无需再修改代码！🎊
