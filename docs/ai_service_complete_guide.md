# AI服务完整使用指南

## 🎯 概述

我已经成功创建了一个通用的AI服务类，并完全重构了Server酱队列系统。现在您拥有一个功能强大的AI工具平台，支持多种AI应用场景。

## 🚀 核心功能

### 1. 通用AI服务类 (`app/utils/AIService.php`)

**主要特性**：
- 🔧 **灵活配置**: 支持自定义API地址、密钥和模型
- 🤖 **多种功能**: 文本摘要、情感分析、内容分类、消息优化
- 📊 **完整日志**: 详细的请求和错误日志记录
- 🛡️ **错误处理**: 完善的异常处理和降级机制

**支持的AI功能**：
1. **服务器告警优化** - 智能优化系统通知消息
2. **文本摘要生成** - 自动生成文本摘要
3. **情感分析** - 分析文本情感倾向
4. **内容分类** - 智能分类文本内容

### 2. 优化的Server酱队列 (`app/queue/redis/ServerSend.php`)

**增强功能**：
- 🤖 **AI智能优化**: 自动优化告警消息内容
- 📊 **完整日志**: 数据库记录所有消息状态
- 🔄 **容错机制**: AI失败时自动降级
- 📈 **统计分析**: 详细的发送统计和成功率

## 📋 API接口列表

### 服务器通知相关

#### 1. 发送服务器通知
```bash
POST /api/sendServerNotice
```

**请求参数**：
```json
{
    "title": "服务器告警",
    "content": "CPU使用率超过90%，请及时处理！",
    "server": "web-server-01",
    "service": "nginx",
    "unionid": "system",
    "enable_ai": true,
    "token": "zksh62576563"
}
```

#### 2. 获取消息统计
```bash
POST /api/getServerNoticeStats
```

### AI工具相关

#### 3. AI文本摘要
```bash
POST /api/aiGenerateSummary
```

**请求参数**：
```json
{
    "text": "需要生成摘要的长文本内容...",
    "max_length": 200
}
```

#### 4. AI情感分析
```bash
POST /api/aiAnalyzeSentiment
```

**请求参数**：
```json
{
    "text": "需要分析情感的文本内容"
}
```

#### 5. AI内容分类
```bash
POST /api/aiClassifyContent
```

**请求参数**：
```json
{
    "text": "需要分类的文本内容",
    "categories": ["技术", "商业", "娱乐"]
}
```

## 🌐 测试页面

### 1. 服务器通知测试
**访问地址**: `http://10.0.0.247/server-notice-test`

**功能**：
- 发送AI优化的服务器通知
- 查看消息统计
- 快速模板选择
- 实时结果展示

### 2. AI工具测试平台
**访问地址**: `http://10.0.0.247/ai-tools-test`

**功能**：
- 文本摘要生成测试
- 情感分析测试
- 内容分类测试
- 统一的测试界面

## 💡 使用示例

### 1. 服务器告警优化示例

**原始消息**：
```
标题: 服务器告警
内容: CPU使用率超过90%
```

**AI优化后**：
```
标题: 🔥 [高优先级] Web服务器CPU资源告警
内容: 
📊 告警详情：
• 服务器：web-server-01
• 监控指标：CPU使用率
• 当前状态：超过安全阈值（90%）
• 影响范围：可能导致服务响应缓慢

🔧 建议操作：
1. 立即检查高CPU占用进程
2. 考虑重启异常服务
3. 必要时进行负载均衡调整

---
🤖 消息优化：AI智能优化
🔥 优先级：high
📂 分类：alert
💡 建议操作：
1. 检查系统进程
2. 重启相关服务
```

### 2. 文本摘要示例

**输入**：
```
人工智能（AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
```

**AI摘要**：
```
人工智能是计算机科学分支，旨在创造类似人类智能的机器。涵盖机器人、语言识别、图像识别等领域，技术日趋成熟，应用不断扩大，未来将成为人类智慧的载体。
```

### 3. 情感分析示例

**输入**：
```
今天的工作进展很顺利，团队合作非常愉快，项目按时完成了！
```

**分析结果**：
```json
{
    "sentiment": "positive",
    "confidence": 0.95,
    "emotions": ["愉快", "满意", "积极"],
    "summary": "文本表达了积极正面的情感，体现了对工作成果的满意和团队合作的认可"
}
```

## 🔧 配置说明

### AI服务配置

在`AIService.php`中可以配置：
```php
// 默认配置
private string $apiUrl = 'https://one.zkshlm.com/v1/chat/completions';
private string $apiKey = 'sk-7QZ7rZhuSbkujNQK35078211C9Ef400c8a07C1Ee72E84854';
private string $defaultModel = 'gemini-2.0-flash-exp';

// 自定义配置
$aiService = new AIService(
    'https://custom-api.com/v1/chat/completions',
    'your-api-key',
    'your-model'
);
```

### Server酱队列配置

在`ServerSend.php`构造函数中：
```php
$this->aiService = new AIService(
    'https://tbai.xin/v1/chat/completions',
    'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO1',
    'gpt-4.1-mini'
);
```

## 📊 数据库设计

### 消息日志表 (`wm_message_logs`)
```sql
- id: 主键
- unionid: 用户标识
- type: 消息类型 (server_notice)
- template_id: 模板ID
- content: 原始内容 (JSON)
- status: 状态 (0:待发送, 1:成功, 2:失败)
- response: 发送响应 (JSON)
- created_at: 创建时间
- updated_at: 更新时间
```

## 🚀 快速开始

### 1. 发送AI优化的服务器通知
```bash
curl -X POST "http://10.0.0.247/api/sendServerNotice" \
-H "Content-Type: application/json" \
-d '{
  "title": "数据库连接异常",
  "content": "MySQL连接超时，影响用户登录",
  "server": "db-server-01",
  "service": "mysql",
  "enable_ai": true,
  "token": "zksh62576563"
}'
```

### 2. 生成文本摘要
```bash
curl -X POST "http://10.0.0.247/api/aiGenerateSummary" \
-H "Content-Type: application/json" \
-d '{
  "text": "您的长文本内容...",
  "max_length": 150
}'
```

### 3. 分析文本情感
```bash
curl -X POST "http://10.0.0.247/api/aiAnalyzeSentiment" \
-H "Content-Type: application/json" \
-d '{
  "text": "今天心情很好，工作很顺利！"
}'
```

## 📈 监控和日志

### 查看AI请求日志
```bash
tail -f runtime/logs/info.log | grep "AI API请求"
```

### 查看错误日志
```bash
tail -f runtime/logs/error.log | grep "AI"
```

### 查看消息统计
访问: `http://10.0.0.247/api/getServerNoticeStats`

## 🎉 总结

现在您拥有了一个完整的AI增强消息系统：

✅ **通用AI服务** - 支持多种AI功能的独立服务类
✅ **智能消息队列** - AI优化的服务器通知系统  
✅ **完整API接口** - 丰富的API接口支持
✅ **可视化测试** - 友好的Web测试界面
✅ **详细日志** - 完善的日志记录和统计
✅ **灵活配置** - 支持自定义AI模型和参数

您可以立即开始使用这些功能，或者基于这个框架扩展更多AI应用场景！
