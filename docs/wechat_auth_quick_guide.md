# 微信授权快速使用指南

## 🎯 概述

系统已配置好微信公众号"静默-升级"混合授权方案，使用预配置的授权链接，开箱即用。

## 🔗 预配置授权链接

### 静默授权 (snsapi_base)
```
https://www.zkshlm.com/login4.php?param={回调地址}&state={状态参数}
```

### 认证授权 (snsapi_userinfo)  
```
https://www.zkshlm.com/login3.php?param={回调地址}&state={状态参数}
```

## 🚀 使用流程

### 1. 用户访问登录页面
- 页面地址: `http://10.0.0.247/login`
- 自动检测微信环境
- 显示微信登录按钮

### 2. 点击微信登录
- 自动跳转到静默授权链接
- 尝试获取用户基本信息
- 无需用户操作（静默）

### 3. 后端处理授权
- 接收微信返回的code
- 调用 `/api/wechatMpUserInfo` 接口
- 智能判断是否需要升级授权

### 4. 三种可能结果

#### 情况1: 静默授权成功 ✅
```json
{
    "code": 0,
    "message": "登录成功"
}
```
**结果**: 直接登录成功，unionid已保存到session，跳转到业务页面

#### 情况2: 需要升级授权 ⚠️
```json
{
    "code": 1001,
    "message": "需要用户授权以获取完整信息",
    "data": {
        "auth_type": "need_upgrade",
        "reason": "首次使用需要授权获取用户信息"
    }
}
```
**结果**: 弹出确认框，用户同意后前端构建认证授权URL并跳转

#### 情况3: 授权失败 ❌
```json
{
    "code": 500,
    "message": "授权失败",
    "data": {
        "auth_type": "error",
        "error_detail": "错误详情"
    }
}
```
**结果**: 显示错误信息，提示重试

## 📱 前端实现要点

### 微信环境检测
```javascript
const isWeixin = computed(() => {
    return /MicroMessenger/i.test(navigator.userAgent);
});
```

### 静默授权跳转
```javascript
const wechatAuthLogin = () => {
    const redirectUri = encodeURIComponent(window.location.href);
    const state = Math.random().toString(36).substring(2, 15);
    
    // 使用预配置的静默授权链接
    const authUrl = `https://www.zkshlm.com/login4.php?param=${redirectUri}&state=${state}`;
    
    window.location.href = authUrl;
};
```

### 处理授权回调
```javascript
const wechatH5Login = async (code, state) => {
    const response = await fetch('/api/wechatMpUserInfo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code, state })
    });

    const result = await response.json();
    
    if (result.code === 0) {
        // 静默授权成功，unionid已保存到session
        showSuccess.value = true;
    } else if (result.code === 1001) {
        // 需要升级授权，前端构建认证授权URL
        if (confirm('首次使用需要授权获取用户信息，是否继续？')) {
            const redirectUri = encodeURIComponent(window.location.href);
            const state = Math.random().toString(36).substring(2, 15);
            const upgradeAuthUrl = `https://www.zkshlm.com/login3.php?param=${redirectUri}&state=${state}`;

            window.location.href = upgradeAuthUrl;
        }
    } else {
        // 授权失败
        alert(result.message);
    }
};
```

## 🔧 后端接口

### wechatMpUserInfo接口
- **路由**: `POST /api/wechatMpUserInfo`
- **功能**: 处理微信授权码，实现静默-升级逻辑

**请求参数**:
```json
{
    "code": "微信授权码",
    "state": "状态参数"
}
```

**核心逻辑**:
1. 使用EasyWeChat的`userFromCode()`获取用户信息
2. 检查是否包含`unionid`字段
3. 有UnionID → 静默授权成功
4. 无UnionID → 需要升级授权
5. 异常处理 → 授权失败

## 🎯 用户体验

### 老用户（已授权过）
1. 点击微信登录
2. 自动跳转微信授权
3. 立即返回，直接登录成功
4. **全程无感知，0操作**

### 新用户（首次使用）
1. 点击微信登录
2. 自动跳转微信授权
3. 返回后提示需要授权
4. 用户确认后跳转认证页面
5. 授权完成后登录成功
6. **后续访问变为老用户体验**

## ✨ 核心优势

- **🎯 零配置**: 使用预配置链接，无需额外设置
- **🚀 智能判断**: 自动识别新老用户，按需升级
- **📱 体验优化**: 老用户无感知，新用户引导清晰
- **🔧 技术成熟**: 基于EasyWeChat，稳定可靠
- **🛡️ 错误处理**: 完善的异常处理和用户提示

## 🔍 调试技巧

### 查看授权流程
1. 打开浏览器开发者工具
2. 查看Console日志输出
3. 观察网络请求和响应

### 常见问题
1. **code已使用**: 微信授权码只能使用一次，刷新页面会导致code失效
2. **域名不匹配**: 确保回调域名在微信后台已配置
3. **UnionID为空**: 确保公众号已绑定到开放平台

现在您的微信授权系统已经完全配置好，使用预配置的授权链接，开箱即用！🎊
