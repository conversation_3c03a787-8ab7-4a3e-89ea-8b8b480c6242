# 微信用户信息获取接口文档

## 📋 接口概述

本文档描述了两个微信用户信息获取接口，分别用于微信开放平台和微信公众号平台的用户信息获取功能。

## 🔗 接口列表

### 1. 微信开放平台用户信息获取
### 2. 微信公众号用户信息获取

---

## 📱 1. 微信开放平台用户信息获取

### 基本信息
- **接口名称**: 微信开放平台code换取微信用户详情
- **请求路径**: `/api/wechatOpenUserInfo`
- **请求方式**: `POST`
- **接口描述**: 通过微信开放平台授权码获取用户详细信息

### 请求参数

#### Headers
```
Content-Type: application/json
```

#### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信开放平台授权码 |

#### 请求示例
```json
{
    "code": "021234567890abcdef"
}
```

### 响应参数

#### 成功响应
```json
{
    "code": 0,
    "message": "获取用户信息成功",
    "data": {
        "openid": "o1234567890abcdef",
        "unionid": "u1234567890abcdef",
        "nickname": "张三",
        "sex": 1,
        "province": "广东",
        "city": "深圳",
        "country": "中国",
        "headimgurl": "https://thirdwx.qlogo.cn/mmopen/...",
        "privilege": [],
        "language": "zh_CN"
    }
}
```

#### 失败响应
```json
{
    "code": 500,
    "message": "获取用户信息失败: invalid code",
    "data": {
        "errcode": 40029,
        "errmsg": "invalid code"
    }
}
```

#### 参数错误响应
```json
{
    "code": 400,
    "message": "缺少授权码参数"
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| data.openid | string | 用户在当前应用的唯一标识 |
| data.unionid | string | 用户在开放平台的唯一标识 |
| data.nickname | string | 用户昵称 |
| data.sex | int | 性别，1男性，2女性，0未知 |
| data.province | string | 省份 |
| data.city | string | 城市 |
| data.country | string | 国家 |
| data.headimgurl | string | 头像URL |
| data.privilege | array | 用户特权信息 |
| data.language | string | 语言 |

### 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 缺少授权码参数 | 检查请求参数是否包含code |
| 500 | 系统异常或微信API错误 | 检查code有效性和网络连接 |

---

## 💬 2. 微信公众号用户信息获取

### 基本信息
- **接口名称**: 微信公众号code换取微信用户详情
- **请求路径**: `/api/wechatMpUserInfo`
- **请求方式**: `POST`
- **接口描述**: 通过微信公众号授权码获取用户详细信息（静默授权）

### 请求参数

#### Headers
```
Content-Type: application/json
```

#### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信公众号授权码 |

#### 请求示例
```json
{
    "code": "031234567890abcdef"
}
```

### 响应参数

#### 成功响应
```json
{
    "code": 0,
    "message": "获取用户信息成功",
    "data": {
        "openid": "ow-9xxNhy14ah2FBJUhZeXqCiJRA",
        "unionid": "u1234567890abcdef",
        "nickname": "李四",
        "sex": 2,
        "province": "北京",
        "city": "北京",
        "country": "中国",
        "headimgurl": "https://thirdwx.qlogo.cn/mmopen/...",
        "privilege": [],
        "language": "zh_CN",
        "subscribe": 1,
        "subscribe_time": 1641888000,
        "remark": "",
        "groupid": 0,
        "tagid_list": []
    }
}
```

#### 失败响应
```json
{
    "code": 500,
    "message": "获取用户信息失败: access_token过期",
    "data": {
        "errcode": 42001,
        "errmsg": "access_token expired"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| data.openid | string | 用户在公众号的唯一标识 |
| data.unionid | string | 用户在开放平台的唯一标识 |
| data.nickname | string | 用户昵称 |
| data.sex | int | 性别，1男性，2女性，0未知 |
| data.province | string | 省份 |
| data.city | string | 城市 |
| data.country | string | 国家 |
| data.headimgurl | string | 头像URL |
| data.privilege | array | 用户特权信息 |
| data.language | string | 语言 |
| data.subscribe | int | 是否关注公众号，1已关注，0未关注 |
| data.subscribe_time | int | 关注时间戳 |
| data.remark | string | 公众号运营者对粉丝的备注 |
| data.groupid | int | 用户所在的分组ID |
| data.tagid_list | array | 用户被打上的标签ID列表 |

## 🔧 使用示例

### JavaScript调用示例

#### 微信开放平台
```javascript
// 获取微信开放平台用户信息
async function getWechatOpenUserInfo(code) {
    try {
        const response = await fetch('/api/wechatOpenUserInfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ code: code })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            console.log('用户信息:', result.data);
            return result.data;
        } else {
            console.error('获取失败:', result.message);
            return null;
        }
    } catch (error) {
        console.error('请求异常:', error);
        return null;
    }
}
```

#### 微信公众号
```javascript
// 获取微信公众号用户信息
async function getWechatMpUserInfo(code) {
    try {
        const response = await fetch('/api/wechatMpUserInfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ code: code })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            console.log('用户信息:', result.data);
            // 保存用户信息到本地存储
            localStorage.setItem('userInfo', JSON.stringify(result.data));
            return result.data;
        } else {
            console.error('获取失败:', result.message);
            return null;
        }
    } catch (error) {
        console.error('请求异常:', error);
        return null;
    }
}
```

### cURL调用示例

#### 微信开放平台
```bash
curl -X POST http://your-domain.com/api/wechatOpenUserInfo \
  -H "Content-Type: application/json" \
  -d '{
    "code": "021234567890abcdef"
  }'
```

#### 微信公众号
```bash
curl -X POST http://your-domain.com/api/wechatMpUserInfo \
  -H "Content-Type: application/json" \
  -d '{
    "code": "031234567890abcdef"
  }'
```

## 🛡️ 安全说明

### 1. 授权码安全
- **一次性使用**: 每个code只能使用一次
- **时效性**: code有效期为10分钟
- **来源验证**: 确保code来自可信的微信授权流程

### 2. 数据保护
- **敏感信息**: 用户信息包含敏感数据，需妥善保护
- **传输安全**: 建议使用HTTPS传输
- **存储安全**: 如需存储用户信息，请遵循数据保护法规

### 3. 频率限制
- **API限制**: 遵循微信API调用频率限制
- **错误处理**: 妥善处理API调用失败的情况
- **重试机制**: 实现合理的重试机制

## 📝 注意事项

### 1. 授权流程
- 确保用户已完成微信授权流程
- 获取的code必须是有效且未使用的
- 不同平台的授权流程和scope不同

### 2. 用户信息更新
- 用户信息可能会发生变化
- 建议定期更新用户信息
- 处理用户取消授权的情况

### 3. 错误处理
- 实现完善的错误处理机制
- 提供用户友好的错误提示
- 记录详细的错误日志便于排查

### 4. 兼容性
- 注意微信API版本更新
- 处理不同微信客户端版本的兼容性
- 测试不同设备和网络环境

## 🔄 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-01-11 | 初始版本，支持微信开放平台和公众号用户信息获取 |

---

**注意**: 本接口文档基于当前实现，如有变更请及时更新文档。
