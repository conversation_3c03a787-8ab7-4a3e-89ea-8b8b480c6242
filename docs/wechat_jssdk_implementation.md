# 微信JSSDK配置接口实现

## 🎯 功能概述

根据EasyWeChat 6.x文档，完成了获取微信JSSDK配置数组的接口，用于前端页面调用微信JS-SDK功能。

## 🚀 接口实现

### 1. 接口信息

#### 请求方式
```
POST /api/getJssdkConfig
```

#### 请求参数
```json
{
    "url": "https://your-domain.com/page.html",  // 必需：当前页面URL
    "apis": [                                     // 可选：需要使用的API列表
        "updateAppMessageShareData",
        "updateTimelineShareData",
        "chooseImage",
        "uploadImage",
        "previewImage",
        "getLocation",
        "openLocation"
    ],
    "debug": false                               // 可选：是否开启调试模式
}
```

#### 响应格式
```json
{
    "code": 0,
    "message": "获取JSSDK配置成功",
    "data": {
        "appId": "wx3cf0f39249eb0exx",
        "jsApiList": [
            "updateAppMessageShareData",
            "updateTimelineShareData"
        ],
        "nonceStr": "mYEeh068LPuWp06u",
        "openTagList": [],
        "signature": "9147682d4f77f7f03162915446f90288cafbda93",
        "timestamp": 1710381708,
        "debug": false
    }
}
```

### 2. 核心实现

#### EasyWeChat 6.x 调用方式
```php
public function getJssdkConfig(Request $request): Response
{
    // 获取请求参数
    $url = $request->input('url');
    $apis = $request->input('apis', []);
    $debug = $request->input('debug', false);
    
    // 验证必要参数
    if (empty($url)) {
        return json(['code' => 400, 'message' => '缺少url参数']);
    }
    
    // 默认API列表
    if (empty($apis)) {
        $apis = [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'chooseImage',
            'uploadImage',
            'previewImage',
            'getLocation',
            'openLocation'
        ];
    }
    
    // 获取微信配置并创建应用实例
    $wechatService = new \app\service\WeChatService();
    $config = $wechatService->getOfficialAccountConfig();
    $app = new \EasyWeChat\OfficialAccount\Application($config);
    
    // 使用EasyWeChat 6.x的工具类生成JSSDK配置
    $utils = $app->getUtils();
    $jssdkConfig = $utils->buildJsSdkConfig(
        url: $url,
        jsApiList: $apis,
        openTagList: [],
        debug: $debug
    );
    
    return json([
        'code' => 0,
        'message' => '获取JSSDK配置成功',
        'data' => $jssdkConfig
    ]);
}
```

## ✨ 技术特点

### 1. EasyWeChat 6.x 兼容
- **新API**: 使用`$app->getUtils()->buildJsSdkConfig()`方法
- **参数命名**: 使用命名参数提高代码可读性
- **返回格式**: 直接返回标准的JSSDK配置数组

### 2. 默认API列表
```php
$defaultApis = [
    'updateAppMessageShareData',  // 分享到朋友圈
    'updateTimelineShareData',    // 分享给朋友
    'onMenuShareTimeline',        // 旧版分享到朋友圈
    'onMenuShareAppMessage',      // 旧版分享给朋友
    'chooseImage',                // 选择图片
    'uploadImage',                // 上传图片
    'previewImage',               // 预览图片
    'getLocation',                // 获取地理位置
    'openLocation'                // 打开地图
];
```

### 3. 参数验证
- **URL验证**: 必须提供当前页面URL
- **API列表**: 可选，有默认值
- **调试模式**: 可选，默认false

## 📊 使用示例

### 1. 前端调用接口
```javascript
// 获取JSSDK配置
fetch('/api/getJssdkConfig', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        url: window.location.href,
        apis: ['updateAppMessageShareData', 'updateTimelineShareData'],
        debug: false
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        // 配置微信JSSDK
        wx.config(data.data);
    }
});
```

### 2. 完整的前端实现
```html
<!DOCTYPE html>
<html>
<head>
    <title>微信JSSDK示例</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
</head>
<body>
    <button onclick="shareToFriend()">分享给朋友</button>
    <button onclick="shareToTimeline()">分享到朋友圈</button>
    
    <script>
        // 获取JSSDK配置
        fetch('/api/getJssdkConfig', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                url: window.location.href,
                apis: ['updateAppMessageShareData', 'updateTimelineShareData']
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                wx.config(data.data);
                
                wx.ready(function() {
                    console.log('微信JSSDK配置成功');
                });
                
                wx.error(function(res) {
                    console.error('微信JSSDK配置失败:', res);
                });
            }
        });
        
        // 分享给朋友
        function shareToFriend() {
            wx.updateAppMessageShareData({
                title: '分享标题',
                desc: '分享描述',
                link: window.location.href,
                imgUrl: 'https://your-domain.com/share-image.jpg',
                success: function() {
                    alert('分享成功');
                }
            });
        }
        
        // 分享到朋友圈
        function shareToTimeline() {
            wx.updateTimelineShareData({
                title: '分享标题',
                link: window.location.href,
                imgUrl: 'https://your-domain.com/share-image.jpg',
                success: function() {
                    alert('分享成功');
                }
            });
        }
    </script>
</body>
</html>
```

### 3. cURL测试
```bash
curl -X POST http://your-domain/api/getJssdkConfig \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://your-domain.com/test.html",
    "apis": ["updateAppMessageShareData", "updateTimelineShareData"],
    "debug": true
  }'
```

## 🛡️ 安全特性

### 1. 参数验证
- **URL验证**: 确保提供有效的页面URL
- **API白名单**: 只允许配置指定的API列表
- **配置验证**: 验证微信公众号配置完整性

### 2. 错误处理
```php
// 参数错误
{
    "code": 400,
    "message": "缺少url参数"
}

// 配置错误
{
    "code": 500,
    "message": "微信公众号配置不完整"
}

// 系统错误
{
    "code": 500,
    "message": "获取JSSDK配置失败: 具体错误信息"
}
```

### 3. 日志记录
- **成功日志**: 记录配置生成成功
- **错误日志**: 详细记录异常信息
- **调试信息**: 包含请求参数和错误堆栈

## 📈 常用API列表

### 基础接口
- `checkJsApi` - 判断当前客户端版本是否支持指定JS接口
- `onMenuShareTimeline` - 分享到朋友圈（旧版）
- `onMenuShareAppMessage` - 分享给朋友（旧版）
- `updateAppMessageShareData` - 分享给朋友（新版）
- `updateTimelineShareData` - 分享到朋友圈（新版）

### 图像接口
- `chooseImage` - 拍照或从手机相册中选图接口
- `previewImage` - 预览图片接口
- `uploadImage` - 上传图片接口
- `downloadImage` - 下载图片接口

### 音频接口
- `startRecord` - 开始录音接口
- `stopRecord` - 停止录音接口
- `onVoiceRecordEnd` - 监听录音自动停止接口
- `playVoice` - 播放语音接口
- `pauseVoice` - 暂停播放接口
- `stopVoice` - 停止播放接口
- `onVoicePlayEnd` - 监听语音播放完毕接口
- `uploadVoice` - 上传语音接口
- `downloadVoice` - 下载语音接口

### 地理位置
- `getLocation` - 获取地理位置接口
- `openLocation` - 使用微信内置地图查看位置接口

### 设备信息
- `getNetworkType` - 获取网络状态接口
- `openProductSpecificView` - 跳转微信商品页接口

### 微信扫一扫
- `scanQRCode` - 调起微信扫一扫接口

## 🎯 最佳实践

### 1. URL处理
- 确保URL是完整的绝对路径
- 去除URL中的hash部分（#后面的内容）
- 处理URL编码问题

### 2. 缓存策略
- JSSDK配置可以缓存一段时间
- 注意access_token的有效期
- 合理设置缓存过期时间

### 3. 错误处理
- 监听wx.error事件
- 提供用户友好的错误提示
- 记录详细的错误日志

现在微信JSSDK配置接口已经完整实现，支持EasyWeChat 6.x，提供了完善的参数验证和错误处理机制！🎊
