<?php
namespace app\middleware;

use Exception;
use support\Request;
use support\Response;

class AdminAuthMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param callable $next
     * @return Response
     * @throws Exception
     */
    public function process(Request $request, callable $next): Response
    {
        // 检查 session 中的用户信息
        $unionid = $request->session()->get('unionid');
        if (empty($unionid)) {
           // 跳转登录路由
           return redirect('/login');
        }
        $authArray = [
            'oZVID6GIspw0ly8kLTulQRssjsIE'
        ];
        // 检查用户是否在授权列表中
        if (!in_array($unionid, $authArray)) {
            return redirect('/login');
        }
        // 将用户ID添加到请求中
        $request->unionid = $unionid;

        return $next($request);
    }
}