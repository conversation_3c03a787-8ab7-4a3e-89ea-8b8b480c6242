<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use support\Medoo;
use Webman\RedisQueue\Redis;
use app\support\RedisCache;

class ApiAuth implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        $startTime = microtime(true);
        
        try {
            // 验证API Token
            $tokenValidation = $this->validateToken($request);
            if (!$tokenValidation['valid']) {
                $response = json([
                    'code' => 401,
                    'message' => $tokenValidation['message']
                ], 401);
                
                // 记录失败的请求日志
                $this->logApiRequest($request, $response, $startTime);
                return $response;
            }

            // 执行请求
            $response = $handler($request);
            
            // 记录成功的请求日志
            $this->logApiRequest($request, $response, $startTime);
            
            return $response;
            
        } catch (\Exception $e) {
            $response = json([
                'code' => 500,
                'message' => '服务器内部错误: ' . $e->getMessage()
            ], 500);
            
            // 记录异常日志
            $this->logApiRequest($request, $response, $startTime);
            return $response;
        }
    }

    /**
     * 验证API Token
     */
    private function validateToken(Request $request): array
    {
        $token = $request->header('Authorization') ?: $request->post('token') ?: $request->get('token');
        
        if (empty($token)) {
            return ['valid' => false, 'message' => '缺少访问令牌'];
        }

        // 移除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        // 临时直接从数据库获取API Token配置
        try {
            // 临时绕过缓存，直接从数据库获取
            $apiToken = RedisCache::get('api_token', function() {
                return Medoo::get('wm_settings', 'value', ['key' => 'api_token']);
            });

            if (empty($apiToken)) {
                return ['valid' => false, 'message' => 'API Token未配置'];
            }

            if ($token !== $apiToken) {
                return ['valid' => false, 'message' => 'API Token无效'];
            }

            return ['valid' => true];

        } catch (\Exception $e) {
            error_log("Debug - Token验证异常: " . $e->getMessage());
            return ['valid' => false, 'message' => 'Token验证失败'];
        }
    }

    /**
     * 记录API请求日志到队列
     */
    private function logApiRequest(Request $request, Response $response, float $startTime): void
    {
        try {
            $finishTime = microtime(true);
            $duration = round(($finishTime - $startTime) * 1000, 2); // 转换为毫秒
            
            // 获取响应状态码
            $statusCode = $response->getStatusCode();

            // 只记录状态码和基本消息，不记录具体响应内容
            $responseInfo = [
                'status_code' => $statusCode,
                'message' => $this->getStatusMessage($statusCode)
            ];

            // 构建日志数据
            $logData = [
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'ip' => $request->getRealIp(),
                'user_agent' => $request->header('User-Agent', ''),
                'params' => json_encode($request->all(), JSON_UNESCAPED_UNICODE),
                'response' => json_encode($responseInfo, JSON_UNESCAPED_UNICODE),
                'status_code' => $statusCode,
                'duration' => $duration,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 发送到队列
            Redis::send('api_log_queue', $logData);
            
        } catch (\Exception $e) {
            // 记录队列发送失败的错误，但不影响主流程
            error_log('Failed to send API log to queue: ' . $e->getMessage());
        }
    }

    /**
     * 获取HTTP状态码对应的消息
     */
    private function getStatusMessage(int $statusCode): string
    {
        $messages = [
            200 => 'OK',
            201 => 'Created',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            422 => 'Unprocessable Entity',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
        ];

        return $messages[$statusCode] ?? 'Unknown Status';
    }
}
