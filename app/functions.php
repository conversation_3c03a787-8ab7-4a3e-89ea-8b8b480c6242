<?php
/**
 * Here is your custom functions.
 */
/**
 * post请求方法
 * @param $url
 * @param array|string $data
 * @param array $headerArray
 * @return array
 */
function httpPost($url, array|string $data = [],array $headerArray = [
    "Content-type:application/json;charset='utf-8'",
    "Accept:application/json"
]): array
{
    $data = json_encode($data);
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headerArray);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_ENCODING, '');
    curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
    curl_setopt($curl, CURLOPT_TIMEOUT, 0);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    $output = curl_exec($curl);
    curl_close($curl);
    if(!$output) {
        return [
            'success' => false,
            'resultMessage' => '请求返回空！'
        ];
    }
    return json_decode($output, true);
}