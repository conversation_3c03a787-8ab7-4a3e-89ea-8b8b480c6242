<?php
namespace app\controller;

use support\Request;
use support\Medoo;
use app\support\RedisCache;
use support\Response;
use support\annotation\Middleware;
use app\middleware\AdminAuthMiddleware;

#[Middleware(AdminAuthMiddleware::class)]
class SettingController
{
    /**
     * 获取系统设置
     */
    public function get(): Response
    {
        try {
            $settings = Medoo::select('wm_settings', ['key', 'value', 'name'],[]);

            $result = [];
            foreach ($settings as $setting) {
                $result[$setting['key']] = [
                    'value' => $setting['value'],
                    'name' => $setting['name']
                ];
            }

            return json([
                'code' => 0,
                'message' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '获取设置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 保存系统设置
     */
    public function save(Request $request): Response
    {
        try {
            $settings = $request->post('settings', []);
            
            if (empty($settings)) {
                return json([
                    'code' => 1,
                    'message' => '设置数据不能为空'
                ]);
            }

            $successCount = 0;
            $errorMessages = [];

            foreach ($settings as $key => $value) {
                try {
                    // 检查设置项是否存在
                    $exists = Medoo::has('wm_settings', ['key' => $key]);

                    if ($exists) {
                        // 更新现有设置
                        $result = Medoo::update('wm_settings', [
                            'value' => $value,
                            'updated_at' => date('Y-m-d H:i:s')
                        ], ['key' => $key]);

                        if ($result->rowCount() > 0) {
                            $successCount++;
                        }
                    } else {
                        // 插入新设置
                        $result = Medoo::insert('wm_settings', [
                            'key' => $key,
                            'value' => $value,
                            'name' => $this->getSettingName($key),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                        if ($result->rowCount() > 0) {
                            $successCount++;
                        }
                    }
                } catch (\Exception $e) {
                    $errorMessages[] = "设置 {$key} 保存失败: " . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                // 清除相关缓存
                $this->clearSettingsCache($settings);

                $message = "成功保存 {$successCount} 项设置";
                if (!empty($errorMessages)) {
                    $message .= "，但有部分失败：" . implode('; ', $errorMessages);
                }

                return json([
                    'code' => 0,
                    'message' => $message,
                    'data' => [
                        'success_count' => $successCount,
                        'error_count' => count($errorMessages)
                    ]
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => '保存失败：' . implode('; ', $errorMessages)
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '保存设置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试微信配置
     */
    public function testWechat(Request $request): Response
    {
        try {
            $appId = $request->post('app_id');
            $appSecret = $request->post('app_secret');

            if (empty($appId) || empty($appSecret)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的微信配置信息'
                ]);
            }

            // 测试获取access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
            $response = httpPost($url, [], []);
            
            if (isset($response['access_token'])) {
                return json([
                    'code' => 0,
                    'message' => '微信配置测试成功',
                    'data' => [
                        'access_token' => substr($response['access_token'], 0, 20) . '...',
                        'expires_in' => $response['expires_in']
                    ]
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => '微信配置测试失败: ' . ($response['errmsg'] ?? '未知错误'),
                    'data' => $response
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试短信配置
     */
    public function testSms(Request $request): Response
    {
        try {
            $accessKey = $request->post('access_key');
            $secretKey = $request->post('secret_key');

            if (empty($accessKey) || empty($secretKey)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的短信配置信息'
                ]);
            }

            // 这里可以添加实际的短信服务测试逻辑
            // 暂时返回模拟结果
            return json([
                'code' => 0,
                'message' => '短信配置测试成功',
                'data' => [
                    'access_key' => substr($accessKey, 0, 10) . '...',
                    'status' => 'connected'
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试微信开放平台配置
     */
    public function testWechatOpen(Request $request): Response
    {
        try {
            $appId = $request->post('app_id');
            $appSecret = $request->post('app_secret');

            if (empty($appId) || empty($appSecret)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的微信开放平台配置信息'
                ]);
            }

            // 测试获取access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
            $response = httpPost($url, [], []);

            if (isset($response['access_token'])) {
                return json([
                    'code' => 0,
                    'message' => '微信开放平台配置测试成功',
                    'data' => [
                        'access_token' => substr($response['access_token'], 0, 20) . '...',
                        'expires_in' => $response['expires_in']
                    ]
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => '微信开放平台配置测试失败: ' . ($response['errmsg'] ?? '未知错误'),
                    'data' => $response
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试微信公众平台配置
     */
    public function testWechatMp(Request $request): Response
    {
        try {
            $appId = $request->post('app_id');
            $appSecret = $request->post('app_secret');

            if (empty($appId) || empty($appSecret)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的微信公众平台配置信息'
                ]);
            }

            // 测试获取access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
            $response = httpPost($url, [], []);

            if (isset($response['access_token'])) {
                return json([
                    'code' => 0,
                    'message' => '微信公众平台配置测试成功',
                    'data' => [
                        'access_token' => substr($response['access_token'], 0, 20) . '...',
                        'expires_in' => $response['expires_in']
                    ]
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => '微信公众平台配置测试失败: ' . ($response['errmsg'] ?? '未知错误'),
                    'data' => $response
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试S3配置
     */
    public function testS3(Request $request): Response
    {
        try {
            $endpoint = $request->post('endpoint');
            $region = $request->post('region');
            $accessKey = $request->post('access_key');
            $secretKey = $request->post('secret_key');
            $bucket = $request->post('bucket');

            if (empty($endpoint) || empty($accessKey) || empty($secretKey) || empty($bucket)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的S3配置信息'
                ]);
            }

            // 这里可以添加实际的S3连接测试逻辑
            // 暂时返回模拟结果
            return json([
                'code' => 0,
                'message' => 'S3配置测试成功',
                'data' => [
                    'endpoint' => $endpoint,
                    'bucket' => $bucket,
                    'status' => 'connected'
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设置项名称
     */
    private function getSettingName(string $key): string
    {
        $names = [
            'system_name' => '系统名称',
            'api_token' => 'API访问令牌',
            'wechat_open_app_id' => '微信开放平台AppID',
            'wechat_open_app_secret' => '微信开放平台AppSecret',
            'wechat_mp_app_id' => '微信公众号AppID',
            'wechat_mp_app_secret' => '微信公众号AppSecret',
            'sms_access_key' => '短信服务AccessKey',
            'sms_secret_key' => '短信服务SecretKey',
            's3_endpoint' => 'S3服务端点',
            's3_region' => 'S3区域',
            's3_access_key' => 'S3 Access Key',
            's3_secret_key' => 'S3 Secret Key',
            's3_bucket' => 'S3存储桶',
            's3_cdn_domain' => 'S3 CDN域名'
        ];

        return $names[$key] ?? $key;
    }

    /**
     * 清除设置相关缓存
     */
    private function clearSettingsCache(array $settings): void
    {
        try {
            // 如果更新了API Token，清除缓存
            if (isset($settings['api_token'])) {
                RedisCache::remove('api_token');
            }

            // 如果更新了微信开放平台配置，清除缓存
            if (isset($settings['wechat_open_app_id'])) {
                RedisCache::remove('wechat_open_app_id');
            }
            if (isset($settings['wechat_open_app_secret'])) {
                RedisCache::remove('wechat_open_app_secret');
            }

            // 如果更新了微信公众平台配置，清除缓存
            if (isset($settings['wechat_mp_app_id'])) {
                RedisCache::remove('wechat_mp_app_id');
            }
            if (isset($settings['wechat_mp_app_secret'])) {
                RedisCache::remove('wechat_mp_app_secret');
            }

        } catch (\Exception $e) {
            error_log('清除设置缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试企业微信配置
     */
    public function testWechatWork(Request $request): Response
    {
        try {
            $corpId = $request->post('corp_id');
            $secret = $request->post('secret');
            $agentId = $request->post('agent_id');

            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->testWorkConfig($corpId, $secret, intval($agentId));

            return json([
                'code' => $result['success'] ? 0 : $result['code'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试企业微信发送消息
     */
    public function testWechatWorkMessage(Request $request): Response
    {
        try {
            $corpId = $request->post('corp_id');
            $secret = $request->post('secret');
            $agentId = $request->post('agent_id');

            if (empty($corpId) || empty($secret) || empty($agentId)) {
                return json([
                    'code' => 1,
                    'message' => '请填写完整的企业微信配置信息'
                ]);
            }

            $wechatService = new \app\service\WeChatService();

            // 发送测试消息给@all（所有成员）
            $result = $wechatService->sendWorkMessage(
                '@all',
                '这是一条来自系统的测试消息，用于验证企业微信配置是否正确。\n\n发送时间：' . date('Y-m-d H:i:s'),
                'text'
            );

            return json([
                'code' => $result['success'] ? 0 : $result['code'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试发送消息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试企业微信获取应用成员
     */
    public function testWechatWorkMembers(Request $request): Response
    {
        try {
            $corpId = $request->post('corp_id');
            $secret = $request->post('secret');
            $agentId = $request->post('agent_id');

            if (empty($corpId) || empty($secret) || empty($agentId)) {
                return json([
                    'code' => 1,
                    'message' => '请填写企业ID、应用Secret和应用ID'
                ]);
            }

            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->getWorkMembers();

            if ($result['success']) {
                $userCount = $result['data']['total_users'] ?? 0;
                $deptCount = $result['data']['total_departments'] ?? 0;

                return json([
                    'code' => 0,
                    'message' => "获取应用成员成功，应用「{$result['data']['agent_info']['name']}」可见范围：{$userCount}个成员，{$deptCount}个部门",
                    'data' => [
                        'agent_info' => $result['data']['agent_info'] ?? [],
                        'total_users' => $userCount,
                        'total_departments' => $deptCount,
                        'members' => array_slice($result['data']['members'] ?? [], 0, 5) // 只返回前5个成员作为示例
                    ]
                ]);
            } else {
                return json([
                    'code' => $result['code'],
                    'message' => $result['message'],
                    'data' => $result['data'] ?? null
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试获取应用成员失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试AI配置
     */
    public function testAI(Request $request): Response
    {
        try {
            $apiUrl = $request->post('api_url');
            $apiKey = $request->post('api_key');
            $model = $request->post('model');
            $timeout = $request->post('timeout', 30);

            $aiService = new \app\utils\AIService();
            $result = $aiService->testAIConfig($apiUrl, $apiKey, $model, intval($timeout));

            return json([
                'code' => $result['success'] ? 0 : $result['code'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '测试AI配置失败: ' . $e->getMessage()
            ]);
        }
    }
}
