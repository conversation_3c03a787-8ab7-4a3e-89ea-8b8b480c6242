<?php
namespace app\controller;

use app\middleware\ApiAuth;
use support\Request;
use support\Medoo;
use support\annotation\Middleware;
use support\Response;
use app\service\WeChatService;
use Webman\RedisQueue\Redis;

#[Middleware(ApiAuth::class)]
class ApiController
{
    /**
     * 获取API统计数据
     */
    public function getStats(): Response
    {
        try {
            // 今日统计
            $today = date('Y-m-d');
            $todayStart = $today . ' 00:00:00';
            $todayEnd = $today . ' 23:59:59';

            // 总请求数
            $totalRequests = Medoo::count('wm_api_logs');

            // 今日请求数
            $todayRequests = Medoo::count('wm_api_logs', [
                'created_at[>=]' => $todayStart,
                'created_at[<=]' => $todayEnd
            ]);

            // 成功请求数（状态码200）
            $successRequests = Medoo::count('wm_api_logs', ['status_code' => 200]);

            // 今日成功请求数
            $todaySuccessRequests = Medoo::count('wm_api_logs', [
                'status_code' => 200,
                'created_at[>=]' => $todayStart,
                'created_at[<=]' => $todayEnd
            ]);

            // 失败请求数（状态码非200）
            $failedRequests = Medoo::count('wm_api_logs', ['status_code[!]' => 200]);

            // 今日失败请求数
            $todayFailedRequests = Medoo::count('wm_api_logs', [
                'status_code[!]' => 200,
                'created_at[>=]' => $todayStart,
                'created_at[<=]' => $todayEnd
            ]);

            // 平均响应时间
            $avgDuration = Medoo::avg('wm_api_logs', 'duration') ?: 0;

            // 接口调用统计
            $endpointStats = Medoo::query("
                SELECT endpoint, COUNT(*) as count, AVG(duration) as avg_duration
                FROM wm_api_logs
                GROUP BY endpoint
                ORDER BY count DESC
                LIMIT 10
            ")->fetchAll();

            // 状态码统计
            $statusStats = Medoo::query("
                SELECT status_code, COUNT(*) as count
                FROM wm_api_logs
                GROUP BY status_code
                ORDER BY count DESC
            ")->fetchAll();

            // 最近7天请求趋势
            $trendData = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $count = Medoo::count('wm_api_logs', [
                    'created_at[>=]' => $date . ' 00:00:00',
                    'created_at[<=]' => $date . ' 23:59:59'
                ]);
                $trendData[] = [
                    'date' => $date,
                    'count' => $count
                ];
            }

            return json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'summary' => [
                        'total_requests' => $totalRequests,
                        'today_requests' => $todayRequests,
                        'success_requests' => $successRequests,
                        'today_success_requests' => $todaySuccessRequests,
                        'failed_requests' => $failedRequests,
                        'today_failed_requests' => $todayFailedRequests,
                        'success_rate' => $totalRequests > 0 ? round($successRequests / $totalRequests * 100, 2) : 0,
                        'today_success_rate' => $todayRequests > 0 ? round($todaySuccessRequests / $todayRequests * 100, 2) : 0,
                        'avg_duration' => round($avgDuration, 2)
                    ],
                    'endpoint_stats' => $endpointStats,
                    'status_stats' => $statusStats,
                    'trend_data' => $trendData
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取API日志
     * 路由: /api/getLogs
     */
    public function getLogs(Request $request)
    {
        try {
            $page = max(1, (int)$request->get('page', 1));
            $limit = min(100, max(1, (int)$request->get('limit', 20)));
            $offset = ($page - 1) * $limit;

            $where = [];

            // 按日期筛选
            if ($request->get('date')) {
                $date = $request->get('date');
                $where['created_at[>=]'] = $date . ' 00:00:00';
                $where['created_at[<=]'] = $date . ' 23:59:59';
            }

            // 按接口筛选
            if ($request->get('endpoint')) {
                $where['endpoint[~]'] = $request->get('endpoint');
            }

            // 按状态码筛选
            if ($request->get('status_code')) {
                $where['status_code'] = (int)$request->get('status_code');
            }

            $total = Medoo::count('wm_api_logs', $where);
            $logs = Medoo::select('wm_api_logs', '*', array_merge($where, [
                'ORDER' => ['id' => 'DESC'],
                'LIMIT' => [$offset, $limit]
            ]));

            return json([
                'code' => 0,
                'message' => '获取日志成功',
                'data' => [
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'logs' => $logs
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取日志失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 微信开放平台 - 通过code获取用户信息
     * 路由: /api/wechatOpenUserInfo
     */
    public function wechatOpenUserInfo(Request $request)
    {
        try {
            $code = $request->post('code');

            if (empty($code)) {
                return json([
                    'code' => 400,
                    'message' => '缺少授权码参数'
                ]);
            }

            $wechatService = new WeChatService();
            $result = $wechatService->getUserInfoFromOpenPlatform($code);

            if ($result['success']) {
                // 登录成功，设置session
                $unionid = $result['data']['unionid'] ?? '';

                // 设置session
                $request->session()->set('unionid', $unionid);

                return json([
                    'code' => 0,
                    'message' => '登录成功'
                ]);
            } else {
                return json([
                    'code' => $result['code'] ?? 500,
                    'message' => $result['message'] ?? '登录失败'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '系统错误: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 微信公众平台 - "静默-升级"混合授权方案
     * 路由: /api/wechatMpUserInfo
     */
    public function wechatMpUserInfo(Request $request)
    {
        $code = $request->input('code');

        if (empty($code)) {
            return json([
                'code' => 400,
                'message' => '缺少授权码参数',
                'data' => null
            ]);
        }

        $wechatService = new WeChatService();

        // Step 1: 尝试静默授权获取用户信息
        $result = $wechatService->tryGetUserInfoSilently($code);

        if ($result['success']) {
            // 静默授权成功，设置session
            $unionid = $result['data']['unionid'] ?? '';

            // 设置session
            $request->session()->set('unionid', $unionid);

            return json([
                'code' => 0,
                'message' => '登录成功'
            ]);
        }

        // Step 2: 静默授权失败，检查错误类型
        $errorCode = $result['error_code'] ?? $result['code'] ?? 500;

        if ($errorCode === 48001) {
            // 权限不足错误，说明是新用户，需要升级授权
            return json([
                'code' => 1001, // 特殊状态码，表示需要用户授权
                'message' => '需要用户授权以获取完整信息',
                'data' => [
                    'auth_type' => 'need_upgrade',
                    'openid' => $result['openid'] ?? '', // 即使失败也可能有openid
                    'reason' => '首次使用需要授权获取用户信息'
                ]
            ]);
        }

        // Step 3: 其他错误
        return json([
            'code' => $result['code'] ?? 500,
            'message' => $result['message'] ?? '授权失败',
            'data' => [
                'auth_type' => 'error',
                'error_detail' => $result['error_detail'] ?? null
            ]
        ]);
    }


    /**
     * 微信公众平台 - 发送模板消息
     * 路由: /api/wechatMpTemplateMessage
     */
    public function wechatMpTemplateMessage(Request $request)
    {
        $openid = $request->post('openid');
        $templateId = $request->post('template_id');
        $data = $request->post('data', []);
        $url = $request->post('url', '');
        $miniprogram = $request->post('miniprogram', []);

        $wechatService = new WeChatService();
        $result = $wechatService->sendTemplateMessage($openid, $templateId, $data, $url, $miniprogram);

        return json([
            'code' => $result['success'] ? 0 : ($result['code'] ?? 500),
            'message' => $result['message'],
            'data' => $result['data'] ?? null
        ]);
    }

    /**
     * 微信公众平台 - 静默授权获取openid
     * 路由: /api/wechatMpGetOpenid
     */
    public function wechatMpGetOpenid(Request $request)
    {
        $code = $request->post('code');

        $wechatService = new WeChatService();
        $result = $wechatService->getOpenidFromCode($code);

        return json([
            'code' => $result['success'] ? 0 : ($result['code'] ?? 500),
            'message' => $result['message'],
            'data' => $result['data'] ?? null
        ]);
    }

    /**
     * 微信公众平台 - 通过openid获取用户详细信息
     * 路由: /api/wechatMpGetUserByOpenid
     */
    public function wechatMpGetUserByOpenid(Request $request)
    {
        $openid = $request->post('openid');

        $wechatService = new WeChatService();
        $result = $wechatService->getUserInfoByOpenid($openid);

        return json([
            'code' => $result['success'] ? 0 : ($result['code'] ?? 500),
            'message' => $result['message'],
            'data' => $result['data'] ?? null
        ]);
    }

    /**
     * 发送企业微信通知（支持AI优化）
     * 路由: /api/sendServerNotice
     */
    public function sendServerNotice(Request $request)
    {
        try {
            $title = $request->post('title');
            $content = $request->post('content', $request->post('des', ''));

            if (empty($title)) {
                return json([
                    'code' => 400,
                    'message' => '标题不能为空'
                ]);
            }

            // 构建队列数据
            $queueData = [
                'title' => $title,
                'content' => $content,
                'des' => $content, // 兼容旧格式
                'template_id' => 'wechat_work_' . date('Ymd')
            ];

            // 推送到队列
            Redis::send('send_server', $queueData);

            return json([
                'code' => 0,
                'message' => '通知已发送到企业微信队列，正在处理中',
                'data' => [
                    'queue_data' => $queueData,
                    'ai_enabled' => true, // 固定开启AI
                    'target_user' => 'YangJinJing',
                    'message_type' => 'text',
                    'service' => getenv('APP_NAME') ?: 'Unknown Service'
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '发送失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AI文本摘要生成
     * 路由: /api/aiGenerateSummary
     */
    public function aiGenerateSummary(Request $request)
    {
        try {
            $text = $request->post('text');
            $maxLength = $request->post('max_length', 200);

            if (empty($text)) {
                return json([
                    'code' => 400,
                    'message' => '文本内容不能为空'
                ]);
            }

            $aiService = new \app\utils\AIService();
            $result = $aiService->generateSummary($text, $maxLength);

            return json([
                'code' => $result['success'] ? 0 : 500,
                'message' => $result['success'] ? '摘要生成成功' : $result['message'],
                'data' => $result['success'] ? ['summary' => $result['summary']] : null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '摘要生成失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AI情感分析
     * 路由: /api/aiAnalyzeSentiment
     */
    public function aiAnalyzeSentiment(Request $request)
    {
        try {
            $text = $request->post('text');

            if (empty($text)) {
                return json([
                    'code' => 400,
                    'message' => '文本内容不能为空'
                ]);
            }

            $aiService = new \app\utils\AIService();
            $result = $aiService->analyzeSentiment($text);

            return json([
                'code' => $result['success'] ? 0 : 500,
                'message' => $result['success'] ? '情感分析成功' : $result['message'],
                'data' => $result['success'] ? $result['data'] : null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '情感分析失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AI内容分类
     * 路由: /api/aiClassifyContent
     */
    public function aiClassifyContent(Request $request)
    {
        try {
            $text = $request->post('text');
            $categories = $request->post('categories', []);

            if (empty($text)) {
                return json([
                    'code' => 400,
                    'message' => '文本内容不能为空'
                ]);
            }

            $aiService = new \app\utils\AIService();
            $result = $aiService->classifyContent($text, $categories);

            return json([
                'code' => $result['success'] ? 0 : 500,
                'message' => $result['success'] ? '内容分类成功' : $result['message'],
                'data' => $result['success'] ? $result['data'] : null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '内容分类失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 企业微信发送应用消息
     * 路由: /api/wechatWorkSendMessage
     */
    public function wechatWorkSendMessage(Request $request)
    {
        try {
            $toUser = $request->post('touser', '@all');
            $message = $request->post('message');
            $msgType = $request->post('msgtype', 'text');

            if (empty($message)) {
                return json([
                    'code' => 400,
                    'message' => '消息内容不能为空'
                ]);
            }

            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->sendWorkMessage($toUser, $message, $msgType);

            return json([
                'code' => $result['success'] ? 0 : $result['code'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '发送企业微信消息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取消息统计数据
     * 路由: /api/messageStats
     */
    public function messageStats(Request $request)
    {
        try {
            // 总体统计
            $totalStats = Medoo::query("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending
                FROM wm_message_logs
            ")->fetch();

            // 微信消息统计
            $wechatStats = Medoo::query("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success
                FROM wm_message_logs
                WHERE type = 'wechat'
            ")->fetch();

            // 短信消息统计
            $smsStats = Medoo::query("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success
                FROM wm_message_logs
                WHERE type = 'sms'
            ")->fetch();

            // 今日统计
            $todayStats = Medoo::query("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed
                FROM wm_message_logs
                WHERE DATE(created_at) = CURDATE()
            ")->fetch();

            // 计算成功率
            $wechatSuccessRate = $wechatStats['total'] > 0 ? round(($wechatStats['success'] / $wechatStats['total']) * 100, 1) : 0;
            $smsSuccessRate = $smsStats['total'] > 0 ? round(($smsStats['success'] / $smsStats['total']) * 100, 1) : 0;
            $todaySuccessRate = $todayStats['total'] > 0 ? round(($todayStats['success'] / $todayStats['total']) * 100, 1) : 0;

            $stats = [
                'total' => intval($totalStats['total']),
                'success' => intval($totalStats['success']),
                'failed' => intval($totalStats['failed']),
                'pending' => intval($totalStats['pending']),
                'wechat' => [
                    'total' => intval($wechatStats['total']),
                    'success' => intval($wechatStats['success']),
                    'success_rate' => $wechatSuccessRate
                ],
                'sms' => [
                    'total' => intval($smsStats['total']),
                    'success' => intval($smsStats['success']),
                    'success_rate' => $smsSuccessRate
                ],
                'today' => [
                    'total' => intval($todayStats['total']),
                    'success' => intval($todayStats['success']),
                    'failed' => intval($todayStats['failed']),
                    'success_rate' => $todaySuccessRate
                ]
            ];

            return json([
                'code' => 0,
                'message' => '获取消息统计成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取消息统计失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取消息列表
     * 路由: /api/messages
     */
    public function messages(Request $request)
    {
        try {
            $page = intval($request->get('page', 1));
            $perPage = intval($request->get('per_page', 10));
            $type = $request->get('type', '');
            $status = $request->get('status', '');
            $keyword = $request->get('keyword', '');

            // 构建查询条件
            $where = [];
            if (!empty($type)) {
                $where['type'] = $type;
            }
            if ($status !== '') {
                $where['status'] = intval($status);
            }
            if (!empty($keyword)) {
                $where['OR'] = [
                    'unionid[~]' => $keyword,
                    'content[~]' => $keyword,
                    'template_id[~]' => $keyword
                ];
            }

            // 计算偏移量
            $offset = ($page - 1) * $perPage;

            // 获取总数
            $total = Medoo::count('wm_message_logs', $where);

            // 获取数据
            $messages = Medoo::select('wm_message_logs', [
                'id',
                'unionid',
                'type',
                'template_id',
                'content',
                'status',
                'response',
                'created_at'
            ], array_merge($where, [
                'ORDER' => ['id' => 'DESC'],
                'LIMIT' => [$offset, $perPage]
            ]));

            // 计算分页信息
            $lastPage = ceil($total / $perPage);

            return json([
                'code' => 0,
                'message' => '获取消息列表成功',
                'data' => [
                    'data' => $messages,
                    'pagination' => [
                        'current_page' => $page,
                        'last_page' => $lastPage,
                        'per_page' => $perPage,
                        'total' => $total
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取消息列表失败: ' . $e->getMessage()
            ]);
        }
    }
}
