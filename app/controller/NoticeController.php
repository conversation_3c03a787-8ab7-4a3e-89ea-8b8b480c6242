<?php
namespace app\controller;

use support\Response;
use support\Request;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class NoticeController
{
    /**
     * 微信公众号服务预约通知
     * @param Request $request
     * @return Response
     */
    public function reserveSend(Request $request): Response
    {
        try {
            $token = $request->input('code');
            $data = $request->input('data');

            // 验证token
            if ($token !== 'dsd45d4a6d78w7d8ad749a7d7af4a4ga54f6as4dsa4g41a51sd2adw4d8s8cx4512c31g45') {
                return json(['code' => 403, 'message' => '无效的访问令牌']);
            }

            // 验证data参数
            if (empty($data)) {
                return json(['code' => 400, 'message' => '缺少data参数']);
            }

            // 如果touser 是数组，则转换为字符串
            if (is_array($data['touser'])) {
                // 日志记录
                \support\Log::info('微信公众号模板消息发送，touser为数组', [
                    'data' => $data
                ]);
                $data['touser'] = $data['touser']['openid'];
            }

            // 验证必要字段
            $requiredFields = ['touser', 'template_id', 'url', 'data'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    return json(['code' => 400, 'message' => "缺少必要字段: {$field}"]);
                }
            }

            // 使用WeChatService发送模板消息
            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->sendTemplateMessage($data);

            if ($result['success']) {
                return json([
                    'code' => 200,
                    'message' => '模板消息发送成功',
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => $result['message'],
                    'data' => $result['data'] ?? []
                ]);
            }

        } catch (\Exception $e) {
            // 记录异常日志
            \support\Log::error('微信公众号模板消息发送异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'data' => $request->input('data')
            ]);

            return json([
                'code' => 500,
                'message' => '系统异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 微信开发平台code换取微信用户详情
     * @param Request $request
     * @return Response
     */
    public function wechatOpenUserInfo(Request $request)
    {
        try {
            $code = $request->post('code');

            if (empty($code)) {
                return json([
                    'code' => 400,
                    'message' => '缺少授权码参数'
                ]);
            }

            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->getUserInfoFromOpenPlatform($code);

            if ($result['success']) {
                return json([
                    'code' => 200,
                    'message' => '获取用户信息成功',
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => $result['message'],
                    'data' => $result['data'] ?? []
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '系统异常: ' . $e->getMessage()
            ]);
        }
    }   

    /**
     * 微信公众号code换取微信用户详情
     * @param Request $request
     * @return Response
     */
    public function wechatMpUserInfo(Request $request)
    {
        try {
            $code = $request->post('code');

            if (empty($code)) {
                return json([
                    'code' => 400,
                    'message' => '缺少授权码参数'
                ]);
            }

            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->tryGetUserInfoSilently($code);

            if ($result['success']) {
                return json([
                    'code' => 200,
                    'message' => '获取用户信息成功',
                    'data' => $result['data']
                ]);
            } else {
                // 检查是否是需要用户授权的情况
                if (isset($result['error_code']) && $result['error_code'] == 48001) {
                    return json([
                        'success' => false,
                        'error_code' => 48001,
                        'code' => 48001,
                        'message' => '需要用户授权以获取完整信息',
                        'openid' => $result['openid'] ?? null,
                        'error_detail' => '静默授权无法获取unionid，需要snsapi_userinfo授权'
                    ]);
                } else {
                    return json([
                        'code' => 500,
                        'message' => $result['message'],
                        'data' => $result['data'] ?? []
                    ]);
                }
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '系统异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 企业微信消息发送
     * @param Request $request
     * @return Response
     */
    public function wechatWorkSendMessage(Request $request)
    {
        try {
            $title = $request->input('title');
            $content = $request->input('content');

            if (empty($content)) {
                return json([
                    'code' => 400,
                    'message' => '消息内容不能为空'
                ]);
            }

            // 构建通知数据
            $notificationData = [
                'title' => "🚨 $title",
                'content' => $content,
            ];

            // 发送到队列
            \support\Redis::send('send_server', $notificationData);

            return json([
                'code' => 200,
                'message' => '企业微信消息已加入发送队列',
                'data' => [
                    'queue_time' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '发送企业微信消息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 微信获取JSSDK的配置数组
     * @param Request $request
     * @return Response
     */
    public function getJssdkConfig(Request $request): Response
    {
        try {
            // 获取请求参数
            $url = $request->input('url');
            $debug = $request->input('debug', false);

            // 验证必要参数
            if (empty($url)) {
                return json([
                    'code' => 400,
                    'message' => '缺少url参数'
                ]);
            }

            // 默认API列表
            $apis = [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'onMenuShareTimeline',
                'onMenuShareAppMessage',
                'chooseImage',
                'uploadImage',
                'previewImage',
                'getLocation',
                'openLocation'
            ];

            // 获取微信公众号配置
            $wechatService = new \app\service\WeChatService();
            $config = $wechatService->getOfficialAccountConfig();

            if (empty($config['app_id']) || empty($config['secret'])) {
                return json([
                    'code' => 500,
                    'message' => '微信公众号配置不完整'
                ]);
            }

            // 创建EasyWeChat应用实例
            $app = new \EasyWeChat\OfficialAccount\Application($config);

            // 获取工具类
            $utils = $app->getUtils();

            // 生成JSSDK配置
            $jssdkConfig = $utils->buildJsSdkConfig(
                url: $url,
                jsApiList: $apis,
                openTagList: [],
                debug: $debug
            );

            return json([
                'code' => 200,
                'message' => '获取JSSDK配置成功',
                'data' => $jssdkConfig
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            \support\Log::error('获取JSSDK配置失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'url' => $request->input('url'),
                'apis' => $request->input('apis')
            ]);

            return json([
                'code' => 500,
                'message' => '获取JSSDK配置失败: ' . $e->getMessage()
            ]);
        }
    }
}
