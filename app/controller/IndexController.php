<?php
namespace app\controller;

use support\Request;
use support\Medoo;
use support\Response;
use support\annotation\Middleware;
use app\middleware\AdminAuthMiddleware;

#[Middleware(AdminAuthMiddleware::class)]
class IndexController
{
    /**
     * 获取统计数据API接口
     */
    public function stats(): Response
    {
        $stats = $this->getStats();

        return json([
            'code' => 0,
            'message' => 'success',
            'data' => $stats
        ]);
    }

    /**
     * 获取统计数据
     */
    private function getStats(): array
    {
        // 用户统计
        $totalUsers = Medoo::count('wm_wechat_users',[]);
        $activeUsers = Medoo::count('wm_wechat_users', ['status' => 1]);
        $todayNewUsers = Medoo::count('wm_wechat_users', [
            'created_at[>=]' => date('Y-m-d 00:00:00')
        ]);

        // 消息统计
        $totalMessages = Medoo::count('wm_message_logs',[]);
        $todayMessages = Medoo::count('wm_message_logs', [
            'created_at[>=]' => date('Y-m-d 00:00:00')
        ]);
        $successMessages = Medoo::count('wm_message_logs', ['status' => 1]);
        $failedMessages = Medoo::count('wm_message_logs', ['status' => 2]);
        $pendingMessages = Medoo::count('wm_message_logs', ['status' => 0]);

        // 成功率计算
        $successRate = $totalMessages > 0 ? round($successMessages / $totalMessages * 100, 2) : 0;

        // 微信消息统计
        $wechatMessages = Medoo::count('wm_message_logs', ['type' => 'wechat']);
        $wechatSuccessMessages = Medoo::count('wm_message_logs', [
            'type' => 'wechat',
            'status' => 1
        ]);
        $wechatSuccessRate = $wechatMessages > 0 ? round($wechatSuccessMessages / $wechatMessages * 100, 2) : 0;

        // 短信统计
        $smsMessages = Medoo::count('wm_message_logs', ['type' => 'sms']);
        $smsSuccessMessages = Medoo::count('wm_message_logs', [
            'type' => 'sms',
            'status' => 1
        ]);
        $smsSuccessRate = $smsMessages > 0 ? round($smsSuccessMessages / $smsMessages * 100, 2) : 0;

        return [
            'users' => [
                'total' => $totalUsers,
                'active' => $activeUsers,
                'today_new' => $todayNewUsers,
                'disabled' => $totalUsers - $activeUsers
            ],
            'messages' => [
                'total' => $totalMessages,
                'today' => $todayMessages,
                'success' => $successMessages,
                'failed' => $failedMessages,
                'pending' => $pendingMessages,
                'success_rate' => $successRate
            ],
            'wechat' => [
                'total' => $wechatMessages,
                'success' => $wechatSuccessMessages,
                'success_rate' => $wechatSuccessRate
            ],
            'sms' => [
                'total' => $smsMessages,
                'success' => $smsSuccessMessages,
                'success_rate' => $smsSuccessRate
            ]
        ];
    }
}
