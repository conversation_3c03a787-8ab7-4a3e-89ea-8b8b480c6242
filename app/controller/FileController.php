<?php
namespace app\controller;

use support\Request;
use support\Response;
use app\utils\S3FileUploader;
use support\annotation\Middleware;
use app\middleware\ApiAuth;

class FileController
{
    /**
     * 上传文件接口
     * 支持哈希去重，避免重复上传相同文件
     */
    public function upload(Request $request): Response
    {
        // 检查是否有文件上传
        $file = $request->file('file');
        if (!$file || !$file->isValid()) {
            return json([
                'code' => 1,
                'message' => '请选择要上传的文件'
            ]);
        }

        // 检查文件大小（限制为50MB）
        $maxSize = 50 * 1024 * 1024; // 50MB
        if ($file->getSize() > $maxSize) {
            return json([
                'code' => 1,
                'message' => '文件大小不能超过50MB'
            ]);
        }

        // 检查文件类型
        $allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv',
            'video/mp4', 'video/avi', 'video/mov',
            'audio/mp3', 'audio/wav', 'audio/aac'
        ];

        $mimeType = $file->getUploadMimeType();
        if (!in_array($mimeType, $allowedTypes)) {
            return json([
                'code' => 1,
                'message' => '不支持的文件类型'
            ]);
        }

        // 获取用户信息（如果有的话）
        $unionid = $request->post('unionid'); // 可选参数

        // 上传文件
        $result = S3FileUploader::uploadFile(
            $file->getRealPath(),
            $file->getUploadName(),
            $unionid
        );

        if ($result['success']) {
            return json([
                'code' => 0,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            return json([
                'code' => 1,
                'message' => $result['message']
            ]);
        }
    }

    /**
     * 删除文件接口
     */
    #[Middleware(ApiAuth::class)]
    public function delete(Request $request): Response
    {
        try {
            $fileId = $request->post('file_id');
            if (!$fileId) {
                return json([
                    'code' => 1,
                    'message' => '请提供文件ID'
                ]);
            }

            $result = S3FileUploader::deleteFile((int)$fileId);

            if ($result['success']) {
                return json([
                    'code' => 0,
                    'message' => $result['message']
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => $result['message']
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '删除失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取文件列表接口
     */
    #[Middleware(ApiAuth::class)]
    public function list(Request $request): Response
    {
        try {
            $unionid = $request->get('unionid');
            if (!$unionid) {
                return json([
                    'code' => 1,
                    'message' => '请提供用户UnionID'
                ]);
            }

            $page = max(1, (int)$request->get('page', 1));
            $limit = min(100, max(1, (int)$request->get('limit', 20)));

            $result = S3FileUploader::getUserFiles($unionid, $page, $limit);

            return json([
                'code' => 0,
                'message' => 'success',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '获取文件列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查文件是否存在（秒传检查）
     */
    #[Middleware(ApiAuth::class)]
    public function checkHash(Request $request): Response
    {
        try {
            $fileHash = $request->post('file_hash');
            if (!$fileHash) {
                return json([
                    'code' => 1,
                    'message' => '请提供文件哈希值'
                ]);
            }

            $file = S3FileUploader::getFileByHash($fileHash);

            if ($file) {
                return json([
                    'code' => 0,
                    'message' => '文件已存在，可以秒传',
                    'data' => [
                        'exists' => true,
                        'file' => [
                            'id' => $file['id'],
                            'url' => $file['url'],
                            'file_name' => $file['file_name'],
                            'size' => $file['size'],
                            'mime_type' => $file['mime_type']
                        ]
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'message' => '文件不存在，需要上传',
                    'data' => [
                        'exists' => false
                    ]
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '检查失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取文件信息
     */
    #[Middleware(ApiAuth::class)]
    public function info(Request $request): Response
    {
        try {
            $fileId = $request->get('file_id');
            if (!$fileId) {
                return json([
                    'code' => 1,
                    'message' => '请提供文件ID'
                ]);
            }

            $file = \support\Medoo::get('wm_files', '*', ['id' => (int)$fileId]);

            if ($file) {
                return json([
                    'code' => 0,
                    'message' => 'success',
                    'data' => $file
                ]);
            } else {
                return json([
                    'code' => 1,
                    'message' => '文件不存在'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '获取文件信息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量上传文件接口
     */
    #[Middleware(ApiAuth::class)]
    public function batchUpload(Request $request): Response
    {
        try {
            $files = $request->file('files');
            if (!$files || !is_array($files)) {
                return json([
                    'code' => 1,
                    'message' => '请选择要上传的文件'
                ]);
            }

            $unionid = $request->post('unionid');
            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($files as $index => $file) {
                if (!$file || !$file->isValid()) {
                    $results[] = [
                        'index' => $index,
                        'success' => false,
                        'message' => '文件无效'
                    ];
                    $failCount++;
                    continue;
                }

                $result = S3FileUploader::uploadFile(
                    $file->getRealPath(),
                    $file->getUploadName(),
                    $unionid
                );

                $results[] = [
                    'index' => $index,
                    'success' => $result['success'],
                    'message' => $result['message'],
                    'data' => $result['success'] ? $result['data'] : null
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return json([
                'code' => 0,
                'message' => "批量上传完成，成功: {$successCount}，失败: {$failCount}",
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($files),
                        'success' => $successCount,
                        'fail' => $failCount
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'message' => '批量上传失败: ' . $e->getMessage()
            ]);
        }
    }
}
