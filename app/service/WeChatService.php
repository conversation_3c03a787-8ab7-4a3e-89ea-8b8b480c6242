<?php
namespace app\service;

use support\Medoo;
use EasyWeChat\OfficialAccount\Application as OfficialAccount;
use EasyWeChat\OpenPlatform\Application as OpenPlatform;
use EasyWeChat\Work\Application as WorkApplication;

/**
 * 微信服务类
 * 封装微信相关的所有操作，包括用户授权、信息获取、消息发送等
 */
class WeChatService
{
    /**
     * 通用配置获取方法
     * @param string $keyPrefix 配置键前缀
     * @return array
     */
    private function getWeChatConfig(string $keyPrefix): array
    {
        $appId = \app\support\RedisCache::get($keyPrefix . '_app_id', function() use ($keyPrefix) {
            return Medoo::get('wm_settings', 'value', ['key' => $keyPrefix . '_app_id']);
        }, 300);

        $secret = \app\support\RedisCache::get($keyPrefix . '_secret', function() use ($keyPrefix) {
            return Medoo::get('wm_settings', 'value', ['key' => $keyPrefix . '_app_secret']);
        }, 300);
        return [
            'app_id' => $appId ?: '',
            'secret' => $secret ?: '',
            'use_stable_access_token' => true, // 使用稳定的access_token
            'http' => [
                'timeout' => 30.0,
                'retry' => [
                    'status_codes' => [429, 500, 502, 503, 504],
                    'max_retries' => 3,
                    'delay' => 1000,
                    'multiplier' => 2
                ]
            ]
        ];
    }

    /**
     * 获取微信开放平台配置
     */
    public function getOpenPlatformConfig(): array
    {
        return $this->getWeChatConfig('wechat_open');
    }

    /**
     * 获取微信公众平台配置
     */
    public function getOfficialAccountConfig(): array
    {
        return $this->getWeChatConfig('wechat_mp');
    }

    /**
     * 获取企业微信配置
     */
    public function getWorkConfig(): array
    {
        $corpId = \app\support\RedisCache::get('wechat_work_corp_id', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'wechat_work_corp_id']);
        }, 300);

        $secret = \app\support\RedisCache::get('wechat_work_secret', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'wechat_work_secret']);
        }, 300);

        $agentId = \app\support\RedisCache::get('wechat_work_agent_id', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'wechat_work_agent_id']);
        }, 300);

        return [
            'corp_id' => $corpId ?: '',
            'secret' => $secret ?: '',
            'agent_id' => intval($agentId ?: 0),
        ];
    }

    /**
     * 微信开放平台 - 通过code获取用户信息
     */
    public function getUserInfoFromOpenPlatform(string $code): array
    {
        try {
            if (empty($code)) {
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => '缺少code参数'
                ];
            }

            $config = $this->getOpenPlatformConfig();
            if (empty($config['app_id']) || empty($config['secret'])) {
                return [
                    'success' => false,
                    'code' => 500,
                    'message' => '微信开放平台配置不完整'
                ];
            }

            // 使用EasyWeChat获取用户信息
            $app = new OpenPlatform($config);

            // 通过code获取用户信息
            $oauth = $app->getOAuth();
            $user = $oauth->userFromCode($code);
            // 转换用户信息为数组格式
            $userArray = [
                'openid' => $user->getId(),
                'nickname' => $user->getNickname(),
                'headimgurl' => $user->getAvatar(),
                'sex' => $user->getRaw()['sex'] ?? 0,
                'province' => $user->getRaw()['province'] ?? '',
                'city' => $user->getRaw()['city'] ?? '',
                'country' => $user->getRaw()['country'] ?? '',
                'unionid' => $user->getRaw()['unionid'] ?? ''
            ];

            // 保存或更新用户信息
            $this->saveUserInfo($userArray, $config['app_id']);

            return [
                'success' => true,
                'code' => 0,
                'message' => '获取用户信息成功',
                'data' => [
                    'openid' => $userArray['openid'] ?? '',
                    'nickname' => $userArray['nickname'] ?? '',
                    'sex' => $userArray['sex'] ?? 0,
                    'province' => $userArray['province'] ?? '',
                    'city' => $userArray['city'] ?? '',
                    'country' => $userArray['country'] ?? '',
                    'headimgurl' => $userArray['headimgurl'] ?? '',
                    'unionid' => $userArray['unionid'] ?? ''
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => '获取用户信息失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * "静默-升级"混合授权方案 - 尝试静默获取用户信息（优化版）
     * @param string $code
     * @return array
     */
    public function tryGetUserInfoSilently(string $code): array
    {
        if (empty($code)) {
            return [
                'success' => false,
                'code' => 400,
                'error_code' => 400,
                'message' => '缺少code参数'
            ];
        }

        $config = $this->getOfficialAccountConfig();
        if (empty($config['app_id']) || empty($config['secret'])) {
            return [
                'success' => false,
                'code' => 500,
                'error_code' => 500,
                'message' => '微信公众平台配置不完整'
            ];
        }

        // 使用EasyWeChat创建应用实例
        $app = new OfficialAccount($config);
        $oauth = $app->getOAuth();

        // Step 1: 获取用户信息
        $user = $oauth->userFromCode($code);

        // Step 2: 从token_response中获取openid和scope（适用于静默授权）
        $tokenResponse = $user->getTokenResponse();
        $openid = $tokenResponse['openid'] ?? '';
        $scope = $tokenResponse['scope'] ?? '';

        if (empty($openid)) {
            return [
                'success' => false,
                'code' => 500,
                'error_code' => 500,
                'message' => '无法获取用户OpenID'
            ];
        }
        // Step 3: 优先从数据库查询已保存的用户信息
        $existingUser = $this->getUserByOpenid($openid, $config['app_id']);
        
        if ($existingUser && !empty($existingUser['unionid'])) {
            // 数据库中已有完整用户信息，直接返回，避免微信API调用
            return [
                'success' => true,
                'code' => 0,
                'message' => '静默授权成功（来自缓存）',
                'data' => $existingUser
            ];
        }

        // Step 4: 检查授权范围
        if ($scope === 'snsapi_base') {
            // 静默授权只能获取openid，无法获取unionid，需要升级授权
            return [
                'success' => false,
                'error_code' => 48001,
                'code' => 48001,
                'message' => '需要用户授权以获取完整信息',
                'openid' => $openid,
                'error_detail' => '静默授权无法获取unionid，需要snsapi_userinfo授权'
            ];
        }

        // Step 5: snsapi_userinfo授权，尝试获取详细用户信息
        $rawData = $user->getRaw();

        // 检查是否有错误
        if (isset($rawData['errcode'])) {
            return [
                'success' => false,
                'error_code' => $rawData['errcode'],
                'code' => $rawData['errcode'],
                'message' => $rawData['errmsg'] ?? '微信API调用失败',
                'openid' => $openid,
                'error_detail' => $rawData
            ];
        }

        // 检查是否包含UnionID
        if (isset($rawData['unionid']) && !empty($rawData['unionid'])) {
            // 用户授权成功，获取到了UnionID
            $userArray = [
                'openid' => $openid,
                'nickname' => $user->getNickname() ?? '',
                'sex' => $rawData['sex'] ?? 0,
                'province' => $rawData['province'] ?? '',
                'city' => $rawData['city'] ?? '',
                'country' => $rawData['country'] ?? '',
                'headimgurl' => $user->getAvatar() ?? '',
                'unionid' => $rawData['unionid']
            ];

            // 保存或更新用户信息到数据库
            $this->saveUserInfo($userArray, $config['app_id']);

            return [
                'success' => true,
                'code' => 0,
                'message' => '用户授权成功',
                'data' => $userArray
            ];
        } else {
            // snsapi_userinfo授权但没有UnionID，可能是未绑定开放平台
            return [
                'success' => false,
                'error_code' => 40001,
                'code' => 40001,
                'message' => '无法获取UnionID，请确认公众号已绑定开放平台',
                'openid' => $openid,
                'error_detail' => '用户授权成功但无unionid'
            ];
        }
    }

    /**
     * 根据openid查询数据库中的用户信息
     * @param string $openid
     * @param string $appId
     * @return array|null
     */
    private function getUserByOpenid(string $openid, string $appId): ?array
    {
        // 使用Medoo查询用户表，获取已保存的用户信息
        $user = Medoo::get('wm_wechat_identities', '*', [
            'openid' => $openid,
            'app_id' => $appId
        ]);

        if ($user && !empty($user['unionid'])) {
            return Medoo::get('wm_wechat_users', '*', [
                'unionid' => $user['unionid']
            ]);
        }

        return null;
    }





    /**
     * 微信公众平台 - 静默授权获取openid
     */
    public function getOpenidFromCode(string $code): array
    {
        try {
            if (empty($code)) {
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => '缺少code参数'
                ];
            }

            $config = $this->getOfficialAccountConfig();
            if (empty($config['app_id']) || empty($config['secret'])) {
                return [
                    'success' => false,
                    'code' => 500,
                    'message' => '微信公众平台配置不完整'
                ];
            }

            // 直接调用微信API获取access_token和openid
            $apiUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";
            $params = [
                'appid' => $config['app_id'],
                'secret' => $config['secret'],
                'code' => $code,
                'grant_type' => 'authorization_code'
            ];

            $queryString = http_build_query($params);
            $tokenInfo = httpPost($apiUrl . '?' . $queryString, [], []);

            if (isset($tokenInfo['errcode'])) {
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => '获取access_token失败: ' . ($tokenInfo['errmsg'] ?? '未知错误'),
                    'data' => $tokenInfo
                ];
            }

            return [
                'success' => true,
                'code' => 0,
                'message' => '获取openid成功',
                'data' => [
                    'openid' => $tokenInfo['openid'] ?? '',
                    'access_token' => $tokenInfo['access_token'] ?? '',
                    'refresh_token' => $tokenInfo['refresh_token'] ?? '',
                    'expires_in' => $tokenInfo['expires_in'] ?? 0,
                    'scope' => $tokenInfo['scope'] ?? ''
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => '获取openid失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 微信公众平台 - 通过openid获取用户详细信息
     */
    public function getUserInfoByOpenid(string $openid): array
    {
        try {
            if (empty($openid)) {
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => '缺少openid参数'
                ];
            }

            $config = $this->getOfficialAccountConfig();
            if (empty($config['app_id']) || empty($config['secret'])) {
                return [
                    'success' => false,
                    'code' => 500,
                    'message' => '微信公众平台配置不完整'
                ];
            }

            // 使用EasyWeChat获取用户详细信息
            $app = new OfficialAccount($config);

            // 获取access_token
            $accessToken = $app->getAccessToken()->getToken();

            // 调用微信API获取用户信息
            $apiUrl = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$accessToken}&openid={$openid}&lang=zh_CN";
            $userInfo = httpPost($apiUrl, [], []);

            if (isset($userInfo['errcode'])) {
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => '获取用户信息失败: ' . ($userInfo['errmsg'] ?? '未知错误'),
                    'data' => $userInfo
                ];
            }

            // 转换用户信息为标准格式
            $userArray = [
                'openid' => $userInfo['openid'] ?? '',
                'nickname' => $userInfo['nickname'] ?? '',
                'headimgurl' => $userInfo['headimgurl'] ?? '',
                'sex' => $userInfo['sex'] ?? 0,
                'province' => $userInfo['province'] ?? '',
                'city' => $userInfo['city'] ?? '',
                'country' => $userInfo['country'] ?? '',
                'unionid' => $userInfo['unionid'] ?? '',
                'subscribe' => $userInfo['subscribe'] ?? 0,
                'subscribe_time' => $userInfo['subscribe_time'] ?? 0,
                'language' => $userInfo['language'] ?? '',
                'remark' => $userInfo['remark'] ?? '',
                'groupid' => $userInfo['groupid'] ?? 0,
                'tagid_list' => $userInfo['tagid_list'] ?? []
            ];

            // 保存或更新用户信息
            $this->saveUserInfo($userArray, $config['app_id']);

            return [
                'success' => true,
                'code' => 0,
                'message' => '获取用户信息成功',
                'data' => $userArray
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => '获取用户信息失败: ' . $e->getMessage()
            ];
        }
    }



    /**
     * 发送微信公众号模板消息（支持完整data格式）
     * @param array $data 模板消息数据
     * @return array
     */
    public function sendTemplateMessage(array $data): array
    {
        try {
            // 验证必要字段
            $requiredFields = ['touser', 'template_id', 'data'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "缺少必要字段: {$field}"
                    ];
                }
            }

            // 获取微信公众号配置
            $config = $this->getOfficialAccountConfig();
            if (empty($config['app_id']) || empty($config['secret'])) {
                return [
                    'success' => false,
                    'message' => '微信公众号配置不完整'
                ];
            }

            // 创建EasyWeChat应用实例
            $app = new OfficialAccount($config);
            $client = $app->getClient();

            // 使用EasyWeChat 6.x的客户端发送模板消息
            $response = $client->postJson('/cgi-bin/message/template/send', $data);

            // 获取响应结果
            $result = $response->toArray();

            // 记录消息到数据库
            $this->recordTemplateMessage($data, $result);

            // 检查发送结果
            if (isset($result['errcode']) && $result['errcode'] === 0) {
                return [
                    'success' => true,
                    'message' => '模板消息发送成功',
                    'data' => [
                        'msgid' => $result['msgid'] ?? null,
                        'touser' => $data['touser']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '模板消息发送失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            \support\Log::error('微信公众号模板消息发送异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => '系统异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 记录模板消息到数据库
     * @param array $data 发送的数据
     * @param array $result 发送结果
     * @return bool
     */
    private function recordTemplateMessage(array $data, array $result): bool
    {
        try {
            // 获取unionid（通过openid查找）
            $config = $this->getOfficialAccountConfig();
            $identity = Medoo::get('wm_wechat_identities', 'unionid', [
                'openid' => $data['touser'],
                'app_id' => $config['app_id']
            ]);

            $unionid = $identity ?: $data['touser']; // 如果找不到unionid，使用openid作为备用

            // 提取模板消息内容
            $content = [];
            if (isset($data['data'])) {
                foreach ($data['data'] as $key => $value) {
                    if (isset($value['value'])) {
                        $content[$key] = $value['value'];
                    }
                }
            }

            // 构建消息记录数据（适配wm_message_logs表结构）
            $messageData = [
                'unionid' => $unionid,
                'type' => 'wechat',
                'template_id' => $data['template_id'],
                'content' => json_encode($content, JSON_UNESCAPED_UNICODE),
                'status' => (isset($result['errcode']) && $result['errcode'] === 0) ? 1 : 2, // 1:成功, 2:失败
                'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 插入消息记录到wm_message_logs表
            $insertResult = Medoo::insert('wm_message_logs', $messageData);

            if ($insertResult) {
                \support\Log::info('模板消息记录保存成功', [
                    'unionid' => $unionid,
                    'template_id' => $data['template_id'],
                    'status' => $messageData['status']
                ]);
                return true;
            } else {
                \support\Log::error('模板消息记录保存失败', [
                    'data' => $messageData,
                    'error' => Medoo::error()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            \support\Log::error('记录模板消息异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return false;
        }
    }

    /**
     * 保存或更新用户信息
     */
    public function saveUserInfo(array $userInfo, string $appId): void
    {
        try {
            $unionid = $userInfo['unionid'] ?? '';
            $openid = $userInfo['openid'] ?? '';

            if (empty($unionid) || empty($openid)) {
                return;
            }

            // 检查用户是否存在
            $existingUser = Medoo::get('wm_wechat_users', '*', ['unionid' => $unionid]);

            $userData = [
                'nickname' => $userInfo['nickname'] ?? '',
                'avatar' => $userInfo['headimgurl'] ?? '',
                'gender' => $userInfo['sex'] ?? 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($existingUser) {
                // 更新用户信息
                Medoo::update('wm_wechat_users', $userData, ['unionid' => $unionid]);
            } else {
                // 创建新用户
                $userData['unionid'] = $unionid;
                $userData['created_at'] = date('Y-m-d H:i:s');
                Medoo::insert('wm_wechat_users', $userData);
            }

            // 检查身份是否存在
            $existingIdentity = Medoo::get('wm_wechat_identities', '*', [
                'unionid' => $unionid,
                'app_id' => $appId
            ]);

            $identityData = [
                'openid' => $openid,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($existingIdentity) {
                // 更新身份信息
                Medoo::update('wm_wechat_identities', $identityData, [
                    'unionid' => $unionid,
                    'app_id' => $appId
                ]);
            } else {
                // 创建新身份
                $identityData['unionid'] = $unionid;
                $identityData['app_id'] = $appId;
                $identityData['name'] = '微信应用';
                $identityData['created_at'] = date('Y-m-d H:i:s');
                Medoo::insert('wm_wechat_identities', $identityData);
            }

        } catch (\Exception $e) {
            error_log('保存用户信息失败: ' . $e->getMessage());
        }
    }



    /**
     * 记录消息发送日志
     */
    public function logMessageSend(string $openid, string $type, string $templateId, array $data, array $result): void
    {
        try {
            // 通过openid和appId获取unionid
            $config = $this->getOfficialAccountConfig();
            $identity = Medoo::get('wm_wechat_identities', 'unionid', [
                'openid' => $openid,
                'app_id' => $config['app_id']
            ]);

            $unionid = $identity ?: $openid; // 如果找不到unionid，使用openid作为备用

            Medoo::insert('wm_message_logs', [
                'unionid' => $unionid,
                'type' => $type,
                'template_id' => $templateId,
                'content' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'status' => isset($result['errcode']) && $result['errcode'] === 0 ? 1 : 2, // 1:成功, 2:失败
                'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            \support\Log::error('记录消息发送日志失败', [
                'error' => $e->getMessage(),
                'openid' => $openid,
                'template_id' => $templateId
            ]);
        }
    }

    /**
     * 验证微信开放平台配置
     */
    public function validateOpenPlatformConfig(): array
    {
        try {
            $config = $this->getOpenPlatformConfig();
            $appId = $config['app_id'];
            $appSecret = $config['secret'];

            if (empty($appId) || empty($appSecret)) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '微信开放平台配置不完整'
                ];
            }

            // 测试获取access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
            $response = httpPost($url, [], []);

            if (isset($response['access_token'])) {
                return [
                    'success' => true,
                    'code' => 0,
                    'message' => '微信开放平台配置测试成功',
                    'data' => [
                        'access_token' => substr($response['access_token'], 0, 20) . '...',
                        'expires_in' => $response['expires_in']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '微信开放平台配置测试失败: ' . ($response['errmsg'] ?? '未知错误'),
                    'data' => $response
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证微信公众平台配置
     */
    public function validateOfficialAccountConfig(): array
    {
        try {
            $config = $this->getOfficialAccountConfig();
            $appId = $config['app_id'];
            $appSecret = $config['secret'];

            if (empty($appId) || empty($appSecret)) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '微信公众平台配置不完整'
                ];
            }

            // 测试获取access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
            $response = httpPost($url, [], []);

            if (isset($response['access_token'])) {
                return [
                    'success' => true,
                    'code' => 0,
                    'message' => '微信公众平台配置测试成功',
                    'data' => [
                        'access_token' => substr($response['access_token'], 0, 20) . '...',
                        'expires_in' => $response['expires_in']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '微信公众平台配置测试失败: ' . ($response['errmsg'] ?? '未知错误'),
                    'data' => $response
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 企业微信发送应用消息
     * @param string $toUser 接收消息的用户ID，多个用户用|分隔，@all表示全部用户
     * @param string $message 消息内容
     * @param string $msgType 消息类型，默认text
     * @return array
     */
    public function sendWorkMessage(string $toUser, string $message, string $msgType = 'text'): array
    {
        try {
            $config = $this->getWorkConfig();

            if (empty($config['corp_id']) || empty($config['secret']) || empty($config['agent_id'])) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '企业微信配置不完整'
                ];
            }

            // 创建企业微信应用实例
            $app = new WorkApplication([
                'corp_id' => $config['corp_id'],
                'secret' => $config['secret'],
                'agent_id' => $config['agent_id'],
            ]);

            // 构建消息体
            $messageData = [
                'touser' => $toUser,
                'msgtype' => $msgType,
                'agentid' => $config['agent_id'],
            ];

            // 根据消息类型设置内容
            switch ($msgType) {
                case 'text':
                    $messageData['text'] = ['content' => $message];
                    break;
                case 'markdown':
                    $messageData['markdown'] = ['content' => $message];
                    break;
                default:
                    $messageData['text'] = ['content' => $message];
            }

            // 发送消息
            $result = $app->getClient()->postJson('cgi-bin/message/send', $messageData)->toArray();;
            if ($result['errcode'] === 0) {
                return [
                    'success' => true,
                    'code' => 0,
                    'message' => '消息发送成功',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'code' => $result['errcode'],
                    'message' => '消息发送失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '发送消息失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取企业微信应用成员列表
     * @return array
     */
    public function getWorkMembers(): array
    {
        try {
            $config = $this->getWorkConfig();

            if (empty($config['corp_id']) || empty($config['secret']) || empty($config['agent_id'])) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '企业微信应用配置不完整'
                ];
            }

            // 创建企业微信应用实例
            $app = new WorkApplication([
                'corp_id' => $config['corp_id'],
                'secret' => $config['secret'], // 使用应用secret
                'agent_id' => $config['agent_id'],
            ]);

            // 获取应用的可见范围内的成员
            $result = $app->getClient()->get('cgi-bin/agent/get', [
                'agentid' => $config['agent_id']
            ]);

            if ($result['errcode'] === 0) {
                $agentInfo = $result;
                $allowUserinfos = $agentInfo['allow_userinfos']['user'] ?? [];
                $allowPartyids = $agentInfo['allow_partys']['partyid'] ?? [];

                // 格式化应用可见成员信息
                $formattedMembers = [];

                // 处理直接指定的用户
                foreach ($allowUserinfos as $userInfo) {
                    $formattedMembers[] = [
                        'userid' => $userInfo['userid'] ?? '',
                        'name' => $userInfo['name'] ?? '',
                        'type' => 'user',
                        'source' => 'direct'
                    ];
                }

                // 处理部门信息
                $departments = [];
                foreach ($allowPartyids as $partyId) {
                    $departments[] = [
                        'department_id' => $partyId,
                        'type' => 'department'
                    ];
                }

                return [
                    'success' => true,
                    'code' => 0,
                    'message' => '获取应用成员列表成功',
                    'data' => [
                        'agent_info' => [
                            'agentid' => $agentInfo['agentid'] ?? '',
                            'name' => $agentInfo['name'] ?? '',
                            'description' => $agentInfo['description'] ?? '',
                        ],
                        'members' => $formattedMembers,
                        'departments' => $departments,
                        'total_users' => count($formattedMembers),
                        'total_departments' => count($departments),
                        'is_reportlocation_flag' => $agentInfo['isreportlocation'] ?? 0,
                        'is_reportenter_flag' => $agentInfo['isreportenter'] ?? 0,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'code' => $result['errcode'],
                    'message' => '获取应用信息失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '获取应用成员失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试企业微信配置
     * @param string $corpId 企业ID
     * @param string $secret 应用Secret
     * @param int $agentId 应用ID
     * @return array
     */
    public function testWorkConfig(string $corpId, string $secret, int $agentId): array
    {
        try {
            if (empty($corpId) || empty($secret) || empty($agentId)) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '企业微信配置参数不完整'
                ];
            }

            // 创建企业微信应用实例
            $app = new WorkApplication([
                'corp_id' => $corpId,
                'secret' => $secret,
                'agent_id' => $agentId,
            ]);

            // 测试获取access_token
            $token = $app->getAccessToken()->getToken();

            if (!empty($token)) {
                return [
                    'success' => true,
                    'code' => 0,
                    'message' => '企业微信配置测试成功'
                ];
            } else {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => '企业微信配置测试失败：无法获取访问令牌'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ];
        }
    }


}
