<?php
namespace app\queue\redis;

use support\Medoo;
use Webman\RedisQueue\Consumer;

class ApiLogConsumer implements Consumer
{
    // 要消费的队列名
    public string $queue = 'api_log_queue';

    // 连接名，对应 config/redis_queue.php 里的连接`
    public string $connection = 'default';

    // 消费
    public function consume($data)
    {
        try {
            // 将日志数据插入数据库
            Medoo::insert('wm_api_logs', [
                'endpoint' => $data['endpoint'] ?? '',
                'method' => $data['method'] ?? '',
                'ip' => $data['ip'] ?? '',
                'user_agent' => $data['user_agent'] ?? '',
                'params' => $data['params'] ?? '',
                'response' => $data['response'] ?? '',
                'status_code' => $data['status_code'] ?? 0,
                'duration' => $data['duration'] ?? 0,
                'created_at' => $data['created_at'] ?? date('Y-m-d H:i:s')
            ]);

            
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('Failed to save API log: ' . $e->getMessage());
            echo "Failed to save API log: " . $e->getMessage() . "\n";
            
            // 返回false会重新入队
            return false;
        }
    }
}
