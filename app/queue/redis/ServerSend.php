<?php

namespace app\queue\redis;

use Exception;
use support\Log;
use support\Medoo;
use Webman\RedisQueue\Consumer;
use app\utils\AIService;

class ServerSend implements Consumer
{
    /**
     * 企业微信 消息发送
     *
     * @var string
     */
    public string $queue = 'send_server';

    /**
     * 连接名
     *
     * @var string
     */
    public string $connection = 'default';

    /**
     * AI服务实例
     * @var AIService
     */
    private AIService $aiService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化AI服务，配置从设置页面获取
        $this->aiService = new AIService();
    }

    /**
     * 消费队列数据
     * @param array $data
     * @return void
     */
    public function consume($data): void
    {
        $logId = null;

        try {
            // 创建消息日志记录
            $logId = $this->createMessageLog($data);

            // 使用AI优化消息内容
            $optimizedData = $this->optimizeMessageWithAI($data);

            // 发送消息
            $result = $this->sendMessage($optimizedData);

            // 更新消息日志状态
            $this->updateMessageLog($logId, $result, 1); // 1 = 成功

            Log::channel('default')->info('消息发送成功', [
                'log_id' => $logId,
                'title' => $optimizedData['title'],
                'result' => $result
            ]);

        } catch (Exception $e) {
            // 更新消息日志状态为失败
            if ($logId) {
                $this->updateMessageLog($logId, ['error' => $e->getMessage()], 2); // 2 = 失败
            }

            Log::channel('error')->error('消息发送失败', [
                'log_id' => $logId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
        }
    }

    /**
     * 创建消息日志记录
     * @param array $data
     * @return int 日志ID
     */
    private function createMessageLog(array $data): int
    {
        $logData = [
            'unionid' => 'system',
            'type' => 'server_notice',
            'template_id' => $data['template_id'] ?? 'server_alert',
            'content' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'status' => 0, // 0 = 待发送
            'created_at' => date('Y-m-d H:i:s')
        ];

        $result = Medoo::insert('wm_message_logs', $logData);
        return Medoo::id();
    }

    /**
     * 使用AI优化消息内容
     * @param array $data
     * @return array
     */
    private function optimizeMessageWithAI(array $data): array
    {
        try {
            $originalTitle = $data['title'] ?? '';
            $originalContent = $data['des'] ?? $data['content'] ?? '';

            // 构建上下文信息
            $context = [
                'service' => getenv('APP_NAME') ?: 'Unknown Service',
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // 调用AI服务优化消息（固定开启）
            $aiResult = $this->aiService->optimizeServerAlert($originalTitle, $originalContent, $context);

            if ($aiResult['success']) {
                return [
                    'title' => $aiResult['optimized_title'],
                    'content' => $aiResult['optimized_content']
                ];
            }

        } catch (Exception $e) {
            Log::channel('default')->warning('AI优化失败，使用原始内容', [
                'error' => $e->getMessage()
            ]);
        }

        // AI优化失败时返回原始数据
        return [
            'title' => $data['title'] ?? '',
            'content' => $data['des'] ?? $data['content'] ?? ''
        ];
    }





    /**
     * 发送消息到企业微信
     * @param array $data
     * @return array
     */
    private function sendMessage(array $data): array
    {
        try {
            $title = $data['title'] ?? '';
            $content = $data['content'] ?? '';
            $service = getenv('APP_NAME') ?: 'Unknown Service'; // 从环境变量获取服务名

            // 构建text格式的消息内容
            $textContent = $this->buildTextContent($title, $content, $service);

            // 检查并处理text内容长度
            $textContent = $this->processTextLength($textContent);

            // 验证text内容
            $validationResult = $this->validateTextContent($textContent);
            if (!$validationResult['valid']) {
                return ['error' => $validationResult['message']];
            }

            // 使用企业微信服务发送消息
            $wechatService = new \app\service\WeChatService();
            $result = $wechatService->sendWorkMessage(
                'YangJinJing', // 指定用户
                $textContent,
                'text' // 消息类型为text
            );
            if ($result['success']) {
                return $result['data'] ?? ['success' => true];
            } else {
                return ['error' => $result['message']];
            }

        } catch (\Exception $e) {
            return ['error' => '发送企业微信消息失败: ' . $e->getMessage()];
        }
    }

    /**
     * 构建text格式的消息内容
     * @param string $title
     * @param string $content
     * @param string $service
     * @return string
     */
    private function buildTextContent(string $title, string $content, string $service): string
    {
        $text = "";

        // 标题部分
        if (!empty($title)) {
            $text .= "{$title}\n\n";
        }

        // 主要内容
        if (!empty($content)) {
            $text .= "{$content}\n\n";
        }

        // 底部信息
        $text .= "---\n";
        $text .= "发送时间: " . date('Y-m-d H:i:s') . "\n";

        if (!empty($service)) {
            $text .= "服务: {$service}";
        }

        return $text;
    }

    /**
     * 处理text内容长度，超过2048字节时使用AI精简
     * @param string $content
     * @return string
     */
    private function processTextLength(string $content): string
    {
        $byteLength = strlen($content);

        // 如果内容长度在限制内，直接返回
        if ($byteLength <= 2048) {
            return $content;
        }

        try {
            Log::channel('default')->info('Text内容超长，尝试AI精简', [
                'original_length' => $byteLength,
                'limit' => 2048
            ]);

            // 使用AI服务精简内容
            $result = $this->aiService->generateSummary($content, 1800); // 留出一些余量

            if ($result['success']) {
                $summarizedContent = $result['summary'];
                $newByteLength = strlen($summarizedContent);

                Log::channel('default')->info('AI精简成功', [
                    'original_length' => $byteLength,
                    'new_length' => $newByteLength
                ]);

                // 如果精简后仍然超长，进行截断
                if ($newByteLength > 2048) {
                    $summarizedContent = mb_substr($summarizedContent, 0, 2000, 'UTF-8') . "\n\n...(内容已精简)";
                }

                return $summarizedContent;
            } else {
                // AI精简失败，直接截断
                Log::channel('default')->warning('AI精简失败，使用截断方式', [
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
                return mb_substr($content, 0, 2000, 'UTF-8') . "\n\n...(内容过长已截断)";
            }

        } catch (\Exception $e) {
            // 异常情况下直接截断
            Log::channel('error')->error('处理text长度时发生异常', [
                'error' => $e->getMessage()
            ]);
            return mb_substr($content, 0, 2000, 'UTF-8') . "\n\n...(内容过长已截断)";
        }
    }

    /**
     * 验证text内容
     * @param string $content
     * @return array
     */
    private function validateTextContent(string $content): array
    {
        // 检查是否为UTF-8编码
        if (!mb_check_encoding($content, 'UTF-8')) {
            return [
                'valid' => false,
                'message' => 'text内容必须是UTF-8编码'
            ];
        }

        // 检查字节长度是否超过2048（此时应该已经处理过长度问题）
        $byteLength = strlen($content);
        if ($byteLength > 2048) {
            return [
                'valid' => false,
                'message' => "text内容仍然超过2048字节限制，当前{$byteLength}字节"
            ];
        }

        // 检查内容是否为空
        if (empty(trim($content))) {
            return [
                'valid' => false,
                'message' => 'text内容不能为空'
            ];
        }

        return [
            'valid' => true,
            'message' => '内容验证通过'
        ];
    }

    /**
     * 更新消息日志状态
     * @param int $logId
     * @param array $response
     * @param int $status
     * @return void
     */
    private function updateMessageLog(int $logId, array $response, int $status): void
    {
        try {
            Medoo::update('wm_message_logs', [
                'status' => $status,
                'response' => json_encode($response, JSON_UNESCAPED_UNICODE),
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $logId]);

        } catch (Exception $e) {
            Log::channel('error')->error('更新消息日志失败', [
                'log_id' => $logId,
                'error' => $e->getMessage()
            ]);
        }
    }
}