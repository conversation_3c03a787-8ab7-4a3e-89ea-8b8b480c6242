<?php
namespace app\utils;

use support\Medoo;
use app\support\RedisCache;

class S3FileUploader
{
    /**
     * 上传文件到S3，支持哈希去重
     * @param string $filePath 本地文件路径
     * @param string $originalName 原始文件名
     * @param string|null $unionid 用户UnionID
     * @return array
     */
    public static function uploadFile(string $filePath, string $originalName, ?string $unionid = null): array
    {
        try {
            // 检查文件是否存在
            if (!file_exists($filePath)) {
                return [
                    'success' => false,
                    'message' => '文件不存在'
                ];
            }

            // 计算文件哈希值
            $fileHash = hash_file('sha256', $filePath);
            
            // 检查文件是否已存在（秒传检查）
            $existingFile = Medoo::get('wm_files', '*', ['file_hash' => $fileHash]);
            if ($existingFile) {
                return [
                    'success' => true,
                    'message' => '文件秒传成功',
                    'data' => [
                        'id' => $existingFile['id'],
                        'url' => $existingFile['url'],
                        'file_name' => $existingFile['file_name'],
                        'size' => $existingFile['size'],
                        'mime_type' => $existingFile['mime_type'],
                        'is_duplicate' => true
                    ]
                ];
            }

            // 获取文件信息
            $fileSize = filesize($filePath);
            $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';
            $fileExtension = pathinfo($originalName, PATHINFO_EXTENSION);
            
            // 生成唯一的对象名
            $objectName = 'uploads/' . date('Y/m/d/') . uniqid() . '.' . $fileExtension;

            // 获取S3配置
            $s3Config = self::getS3Config();
            if (!$s3Config['success']) {
                return $s3Config;
            }

            // 上传到S3
            $uploadResult = self::uploadToS3($filePath, $objectName, $s3Config['config']);
            if (!$uploadResult['success']) {
                return $uploadResult;
            }

            // 生成访问URL
            $url = self::generateFileUrl($objectName, $s3Config['config']);

            // 保存文件记录到数据库
            $fileId = Medoo::insert('wm_files', [
                'unionid' => $unionid,
                'file_hash' => $fileHash,
                'file_name' => $originalName,
                'object_name' => $objectName,
                'bucket_name' => $s3Config['config']['bucket'],
                'url' => $url,
                'size' => $fileSize,
                'mime_type' => $mimeType,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if (!$fileId) {
                return [
                    'success' => false,
                    'message' => '保存文件记录失败'
                ];
            }

            return [
                'success' => true,
                'message' => '文件上传成功',
                'data' => [
                    'id' => $fileId,
                    'url' => $url,
                    'file_name' => $originalName,
                    'size' => $fileSize,
                    'mime_type' => $mimeType,
                    'is_duplicate' => false
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除文件
     * @param int $fileId 文件ID
     * @return array
     */
    public static function deleteFile(int $fileId): array
    {
        try {
            // 获取文件信息
            $file = Medoo::get('wm_files', '*', ['id' => $fileId]);
            if (!$file) {
                return [
                    'success' => false,
                    'message' => '文件不存在'
                ];
            }

            // 检查是否有其他记录使用相同的哈希值（相同文件）
            $sameHashCount = Medoo::count('wm_files', ['file_hash' => $file['file_hash']]);
            
            // 如果只有一个记录使用这个哈希值，才从S3删除实际文件
            if ($sameHashCount <= 1) {
                $s3Config = self::getS3Config();
                if ($s3Config['success']) {
                    self::deleteFromS3($file['object_name'], $s3Config['config']);
                }
            }

            // 删除数据库记录
            $deleted = Medoo::delete('wm_files', ['id' => $fileId]);
            
            return [
                'success' => $deleted > 0,
                'message' => $deleted > 0 ? '文件删除成功' : '文件删除失败'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取S3配置
     * @return array
     */
    private static function getS3Config(): array
    {
        $endpoint = getConfig('s3_endpoint');
        $region = getConfig('s3_region', 'us-east-1');
        $accessKey = getConfig('s3_access_key');
        $secretKey = getConfig('s3_secret_key');
        $bucket = getConfig('s3_bucket');
        $cdnDomain = getConfig('s3_cdn_domain');

        if (empty($endpoint) || empty($accessKey) || empty($secretKey) || empty($bucket)) {
            return [
                'success' => false,
                'message' => 'S3配置不完整，请检查配置信息'
            ];
        }

        return [
            'success' => true,
            'config' => [
                'key' => $accessKey,
                'secret' => $secretKey,
                'region' => $region,
                'version' => 'latest',
                'endpoint' => $endpoint,
                'bucket' => $bucket,
                'cdn_domain' => $cdnDomain
            ]
        ];
    }

    /**
     * 上传文件到S3
     * @param string $filePath 本地文件路径
     * @param string $objectName S3对象名
     * @param array $config S3配置
     * @return array
     */
    private static function uploadToS3(string $filePath, string $objectName, array $config): array
    {
        try {
            $objectClient = new \Minoi\Client\ObjectClient($config);
            $result = $objectClient->putObjectBySavePath($filePath, $objectName);
            
            return [
                'success' => true,
                'message' => '上传成功',
                'result' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'S3上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 从S3删除文件
     * @param string $objectName S3对象名
     * @param array $config S3配置
     * @return array
     */
    private static function deleteFromS3(string $objectName, array $config): array
    {
        try {
            $objectClient = new \Minoi\Client\ObjectClient($config);
            $result = $objectClient->removeObject($objectName);
            
            return [
                'success' => true,
                'message' => '删除成功',
                'result' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'S3删除失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成文件访问URL
     * @param string $objectName S3对象名
     * @param array $config S3配置
     * @return string
     */
    private static function generateFileUrl(string $objectName, array $config): string
    {
        // 如果配置了CDN域名，使用CDN域名
        if (!empty($config['cdn_domain'])) {
            return rtrim($config['cdn_domain'], '/') . '/' . $config['bucket'] . '/' . $objectName;
        }
        
        // 否则使用S3 endpoint
        $endpoint = rtrim($config['endpoint'], '/');
        return $endpoint . '/' . $config['bucket'] . '/' . $objectName;
    }

    /**
     * 根据文件哈希获取文件信息
     * @param string $fileHash 文件哈希值
     * @return array|null
     */
    public static function getFileByHash(string $fileHash): ?array
    {
        return Medoo::get('wm_files', '*', ['file_hash' => $fileHash]);
    }

    /**
     * 根据用户获取文件列表
     * @param string $unionid 用户UnionID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserFiles(string $unionid, int $page = 1, int $limit = 20): array
    {
        $offset = ($page - 1) * $limit;
        
        $files = Medoo::select('wm_files', '*', [
            'unionid' => $unionid,
            'ORDER' => ['created_at' => 'DESC'],
            'LIMIT' => [$offset, $limit]
        ]);
        
        $total = Medoo::count('wm_files', ['unionid' => $unionid]);
        
        return [
            'files' => $files,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
}
