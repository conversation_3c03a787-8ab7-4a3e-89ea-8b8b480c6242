<?php

namespace app\utils;

use Exception;
use support\Log;
use support\Medoo;

/**
 * 通用AI服务类
 * 支持多种AI模型和不同的应用场景
 */
class AIService
{
    /**
     * 获取AI配置
     * @return array
     */
    private function getAIConfig(): array
    {
        $apiUrl = \app\support\RedisCache::get('ai_api_url', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'ai_api_url']);
        }, 300);

        $apiKey = \app\support\RedisCache::get('ai_api_key', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'ai_api_key']);
        }, 300);

        $model = \app\support\RedisCache::get('ai_model', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'ai_model']);
        }, 300);

        $timeout = \app\support\RedisCache::get('ai_timeout', function() {
            return Medoo::get('wm_settings', 'value', ['key' => 'ai_timeout']);
        }, 300);

        return [
            'api_url' => $apiUrl ?: 'https://tbai.xin/v1/chat/completions',
            'api_key' => $apiKey ?: 'sk-8I5KTWNcmLXeJGTxk6lVaG5RxilrtfTg39OI2LmmdkJgSMO4',
            'model' => $model ?: 'gpt-4.1-mini',
            'timeout' => intval($timeout ?: 30),
        ];
    }

    /**
     * 发送聊天请求
     * @param array $messages 消息数组
     * @param array $options 选项参数
     * @return array
     */
    public function sendChatRequest(array $messages, array $options = []): array
    {
        try {
            // 获取AI配置
            $config = $this->getAIConfig();

            // 默认参数
            $defaultOptions = [
                'model' => $config['model'],
                'temperature' => 0.2,
                'max_tokens' => 1000,
                'stream' => false,
                'tools' => null
            ];

            // 合并选项
            $options = array_merge($defaultOptions, $options);

            // 构建请求数据
            $requestData = [
                'messages' => $messages,
                'model' => $options['model'],
                'stream' => $options['stream'],
                'temperature' => $options['temperature'],
                'max_tokens' => $options['max_tokens']
            ];

            // 添加工具（如果有）
            if ($options['tools']) {
                $requestData['tools'] = $options['tools'];
            }

            // 设置请求头
            $headerArray = [
                "Content-type:application/json;charset='utf-8'",
                "Accept:application/json",
                "Authorization:Bearer " . $config['api_key']
            ];

            // 发送请求
            $response = httpPost($config['api_url'], $requestData, $headerArray);

            // 记录请求日志
            Log::channel('default')->info('AI API请求', [
                'model' => $options['model'],
                'messages_count' => count($messages),
                'response_status' => isset($response['error']) ? 'error' : 'success'
            ]);

            return $response;

        } catch (Exception $e) {
            Log::channel('error')->error('AI API请求失败', [
                'error' => $e->getMessage(),
                'messages' => $messages
            ]);

            return [
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => 'request_failed'
                ]
            ];
        }
    }

    /**
     * 测试AI配置
     * @param string $apiUrl API地址
     * @param string $apiKey API密钥
     * @param string $model 模型名称
     * @param int $timeout 超时时间
     * @return array
     */
    public function testAIConfig(string $apiUrl, string $apiKey, string $model, int $timeout = 30): array
    {
        try {
            if (empty($apiUrl) || empty($apiKey) || empty($model)) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => 'AI配置参数不完整'
                ];
            }

            // 构建测试消息
            $messages = [
                ['role' => 'user', 'content' => '请回复"测试成功"']
            ];

            // 构建请求数据
            $requestData = [
                'messages' => $messages,
                'model' => $model,
                'temperature' => 0.1,
                'max_tokens' => 50
            ];

            // 设置请求头
            $headerArray = [
                "Content-type:application/json;charset='utf-8'",
                "Accept:application/json",
                "Authorization:Bearer " . $apiKey
            ];

            // 发送测试请求
            $response = httpPost($apiUrl, $requestData, $headerArray);

            if (isset($response['error'])) {
                return [
                    'success' => false,
                    'code' => 1,
                    'message' => 'AI API测试失败: ' . json_encode($response['error'], JSON_UNESCAPED_UNICODE)
                ];
            }

            if (isset($response['choices'][0]['message']['content'])) {
                return [
                    'success' => true,
                    'code' => 0,
                    'message' => 'AI配置测试成功，模型响应正常'
                ];
            }

            return [
                'success' => false,
                'code' => 1,
                'message' => 'AI API返回格式异常'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => 1,
                'message' => '测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 优化服务器告警消息
     * @param string $title 原始标题
     * @param string $content 原始内容
     * @param array $context 上下文信息
     * @return array
     */
    public function optimizeServerAlert(string $title, string $content, array $context = []): array
    {
        $systemPrompt = "你是一个专业的系统消息优化助手。你的任务是优化服务器告警和通知消息，使其更加清晰、专业且易于理解。

请根据以下原则优化消息：
1. 标题要简洁明了，突出关键信息，添加合适的emoji图标
2. 内容要结构化，包含问题描述、影响范围、建议操作
3. 使用专业但易懂的语言
4. 根据消息内容判断优先级（high/medium/low）
5. 分类消息类型（alert/warning/info/maintenance）

请以JSON格式返回优化结果：
{
    \"optimized_title\": \"优化后的标题\",
    \"optimized_content\": \"优化后的内容\",
    \"priority\": \"优先级\",
    \"category\": \"分类\",
    \"suggestions\": [\"建议操作1\", \"建议操作2\"]
}";

        $userPrompt = "原始标题：{$title}\n原始内容：{$content}";

        // 添加上下文信息
        if (!empty($context['server'])) {
            $userPrompt .= "\n服务器：{$context['server']}";
        }
        if (!empty($context['service'])) {
            $userPrompt .= "\n服务：{$context['service']}";
        }
        if (!empty($context['timestamp'])) {
            $userPrompt .= "\n时间：{$context['timestamp']}";
        }

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $userPrompt]
        ];

        $response = $this->sendChatRequest($messages, [
            'temperature' => 0.3,
            'max_tokens' => 1500
        ]);

        return $this->parseOptimizationResponse($response, $title, $content);
    }

    /**
     * 文本摘要生成
     * @param string $text 原始文本
     * @param int $maxLength 最大长度
     * @return array
     */
    public function generateSummary(string $text, int $maxLength = 200): array
    {
        $systemPrompt = "你是一个专业的文本摘要助手。请为给定的文本生成简洁、准确的摘要。

要求：
1. 摘要长度不超过{$maxLength}字符
2. 保留关键信息和要点
3. 语言简洁明了
4. 保持原文的主要意思

请直接返回摘要内容，不需要其他格式。";

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $text]
        ];

        $response = $this->sendChatRequest($messages, [
            'temperature' => 0.2,
            'max_tokens' => 500
        ]);

        if (isset($response['error'])) {
            return [
                'success' => false,
                'message' => 'AI摘要生成失败：' . json_encode($response['error'], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (isset($response['choices'][0]['message']['content'])) {
            return [
                'success' => true,
                'summary' => trim($response['choices'][0]['message']['content'])
            ];
        }

        return [
            'success' => false,
            'message' => '未能生成摘要'
        ];
    }

    /**
     * 情感分析
     * @param string $text 待分析文本
     * @return array
     */
    public function analyzeSentiment(string $text): array
    {
        $systemPrompt = "你是一个专业的情感分析助手。请分析给定文本的情感倾向。

请以JSON格式返回分析结果：
{
    \"sentiment\": \"positive/negative/neutral\",
    \"confidence\": 0.95,
    \"emotions\": [\"具体情感1\", \"具体情感2\"],
    \"summary\": \"情感分析总结\"
}";

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $text]
        ];

        $response = $this->sendChatRequest($messages, [
            'temperature' => 0.1,
            'max_tokens' => 500
        ]);

        return $this->parseJsonResponse($response, '情感分析');
    }

    /**
     * 内容分类
     * @param string $text 待分类文本
     * @param array $categories 可选分类列表
     * @return array
     */
    public function classifyContent(string $text, array $categories = []): array
    {
        $categoriesText = empty($categories) 
            ? "请自动识别合适的分类" 
            : "可选分类：" . implode(', ', $categories);

        $systemPrompt = "你是一个专业的内容分类助手。请为给定的文本内容进行分类。

{$categoriesText}

请以JSON格式返回分类结果：
{
    \"category\": \"主要分类\",
    \"subcategory\": \"子分类\",
    \"confidence\": 0.95,
    \"tags\": [\"标签1\", \"标签2\"],
    \"reasoning\": \"分类理由\"
}";

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $text]
        ];

        $response = $this->sendChatRequest($messages, [
            'temperature' => 0.2,
            'max_tokens' => 800
        ]);

        return $this->parseJsonResponse($response, '内容分类');
    }

    /**
     * 解析优化响应
     * @param array $response AI响应
     * @param string $originalTitle 原始标题
     * @param string $originalContent 原始内容
     * @return array
     */
    private function parseOptimizationResponse(array $response, string $originalTitle, string $originalContent): array
    {
        if (isset($response['error'])) {
            return [
                'success' => false,
                'message' => 'AI优化失败：' . json_encode($response['error'], JSON_UNESCAPED_UNICODE),
                'original_title' => $originalTitle,
                'original_content' => $originalContent
            ];
        }

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            
            // 移除markdown的json标记
            $content = preg_replace('/```json\s*/', '', $content);
            $content = preg_replace('/\s*```/', '', $content);
            
            try {
                $optimizedData = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($optimizedData)) {
                    return [
                        'success' => true,
                        'optimized_title' => $optimizedData['optimized_title'] ?? $originalTitle,
                        'optimized_content' => $optimizedData['optimized_content'] ?? $originalContent,
                        'priority' => $optimizedData['priority'] ?? 'normal',
                        'category' => $optimizedData['category'] ?? 'general',
                        'suggestions' => $optimizedData['suggestions'] ?? [],
                        'original_title' => $originalTitle,
                        'original_content' => $originalContent
                    ];
                }
            } catch (Exception $e) {
                Log::channel('error')->warning('AI响应JSON解析失败', [
                    'content' => $content,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => false,
            'message' => '未能解析AI优化结果',
            'original_title' => $originalTitle,
            'original_content' => $originalContent
        ];
    }

    /**
     * 解析JSON响应
     * @param array $response AI响应
     * @param string $taskName 任务名称
     * @return array
     */
    private function parseJsonResponse(array $response, string $taskName): array
    {
        if (isset($response['error'])) {
            return [
                'success' => false,
                'message' => "{$taskName}失败：" . json_encode($response['error'], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            
            // 移除markdown的json标记
            $content = preg_replace('/```json\s*/', '', $content);
            $content = preg_replace('/\s*```/', '', $content);
            
            try {
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    return [
                        'success' => true,
                        'data' => $data
                    ];
                }
            } catch (Exception $e) {
                Log::channel('warning')->warning("{$taskName}响应JSON解析失败", [
                    'content' => $content,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => false,
            'message' => "未能解析{$taskName}结果"
        ];
    }
}
