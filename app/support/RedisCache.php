<?php
namespace app\support;

use support\Log;
use support\Redis;

class RedisCache
{

    // 获取带前缀的key
    private static function getPrefixKey($key): string
    {
        // 前缀
        $prefix = 'common_cache';
        // 加前缀
        return implode('_', [$prefix, $key]);
    }

    // 获取redis缓存数据
    public static function get($key, $callback = null, $expire = 3600)
    {
        // 缓存key
        $key = trim($key);

        // 缓存key无效
        if (strlen($key) < 1) {
            return null;
        }

        // key加前缀
        $key = static::getPrefixKey($key);

        try {
            // 获取数据
            $raw = Redis::get($key);
        } catch (\Throwable $e) {
            // 记录错误日志
            Log::error('[redis-cache-error]' . $e->getMessage());
            return null;
        }

        // 命中缓存，直接返回数据
        if ($raw !== false && $raw !== null) {
            // 将原始数据反序列化为数组
            $array = unserialize($raw);
            return $array['value'];
        }

        // 如果没有回调函数，直接返回null
        if (!$callback) {
            return null;
        }

        // 获取缓存value
        $value = $callback();

        if (is_null($value)) {
            return null;
        }

        // 缓存时间
        $expire = intval($expire);
        $expire = $expire > 0 ? $expire : 3600;
        $expire = min($expire, 86400); // 最大缓存24小时

        // 设置缓存
        Redis::setEx($key, $expire, serialize(['value' => $value]));

        return $value;
    }

    // 设置缓存
    public static function set($key, $value, $expire = 3600)
    {
        // 缓存key
        $key = trim($key);

        // 缓存key无效
        if (strlen($key) < 1) {
            return false;
        }

        // key加前缀
        $key = static::getPrefixKey($key);

        // 缓存时间
        $expire = intval($expire);
        $expire = $expire > 0 ? $expire : 3600;
        $expire = min($expire, 86400); // 最大缓存24小时

        try {
            // 设置缓存
            return Redis::setEx($key, $expire, serialize(['value' => $value]));
        } catch (\Throwable $e) {
            // 记录错误日志
            Log::error('[redis-cache-error]' . $e->getMessage());
            return false;
        }
    }

    // 删除缓存key
    public static function remove($key)
    {
        // 缓存key
        $key = trim($key);

        // 缓存key无效
        if (strlen($key) < 1) {
            return false;
        }

        // key加前缀
        $key = static::getPrefixKey($key);

        try {
            // 删除缓存key
            $result = Redis::del($key);
            return $result > 0;
        } catch (\Throwable $e) {
            // 记录错误日志
            Log::error('[redis-cache-error]' . $e->getMessage());
            return false;
        }
    }

    // 清空所有缓存
    public static function clear()
    {
        try {
            // 获取前缀
            $prefix = 'common_cache';

            // 获取所有匹配的key
            $keys = Redis::keys($prefix . '_*');

            if (!empty($keys)) {
                // 删除所有匹配的key
                return Redis::del($keys);
            }

            return true;
        } catch (\Throwable $e) {
            // 记录错误日志
            Log::error('[redis-cache-error]' . $e->getMessage());
            return false;
        }
    }
}
