<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息管理 - 中科生活消息平台</title>

    <!-- Bootstrap CSS -->
    <link href="/common/bootstrap-5.3.0-dist/css/bootstrap.css" rel="stylesheet">

    <!-- 引入vue3 -->
    <script src="/common/vue.global.js"></script>

    <!-- Axios -->
    <script src="/common/axios.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border: none;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: #f8f9fa;
            color: #6c757d;
            margin-right: 5px;
        }
        
        .nav-tabs .nav-link.active {
            background: #007bff;
            color: white;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            color: #667eea;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            opacity: 0.8;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .test-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .test-result.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 返回按钮 -->
        <a href="/" class="btn-back">
            ← 返回首页
        </a>

        <div class="container main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><span class="me-3" style="font-size: 2rem;">📧</span>消息管理中心</h1>
                <p>查看消息发送日志、统计数据和发送测试</p>
            </div>

            <!-- 消息管理导航 -->
            <div class="content-card">
                <ul class="nav nav-tabs mb-4" id="messageTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                            📊 消息仪表盘
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="list-tab" data-bs-toggle="tab" data-bs-target="#list" type="button" role="tab" @click="loadMessages">
                            📋 消息列表
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="test-tab" data-bs-toggle="tab" data-bs-target="#test" type="button" role="tab">
                            🧪 发送测试
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="messageTabContent">
                    <!-- 消息仪表盘 -->
                    <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-card text-center border-primary">
                                    <h5 class="text-primary">📧 总消息数</h5>
                                    <h2 class="text-primary">{{ messageStats.total }}</h2>
                                    <small class="text-muted">累计发送消息</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card text-center border-success">
                                    <h5 class="text-success">✅ 成功发送</h5>
                                    <h2 class="text-success">{{ messageStats.success }}</h2>
                                    <small class="text-muted">发送成功数量</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card text-center border-danger">
                                    <h5 class="text-danger">❌ 发送失败</h5>
                                    <h2 class="text-danger">{{ messageStats.failed }}</h2>
                                    <small class="text-muted">发送失败数量</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card text-center border-warning">
                                    <h5 class="text-warning">⏰ 待处理</h5>
                                    <h2 class="text-warning">{{ messageStats.pending }}</h2>
                                    <small class="text-muted">队列中消息</small>
                                </div>
                            </div>
                        </div>

                        <!-- 消息类型统计 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="stat-card">
                                    <h6 class="mb-3">💬 微信消息统计</h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h5 text-info">{{ messageStats.wechat.total }}</div>
                                            <small>总数</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-success">{{ messageStats.wechat.success }}</div>
                                            <small>成功</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-primary">{{ messageStats.wechat.success_rate }}%</div>
                                            <small>成功率</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stat-card">
                                    <h6 class="mb-3">📱 短信消息统计</h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h5 text-info">{{ messageStats.sms.total }}</div>
                                            <small>总数</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-success">{{ messageStats.sms.success }}</div>
                                            <small>成功</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-primary">{{ messageStats.sms.success_rate }}%</div>
                                            <small>成功率</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 今日统计 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="stat-card">
                                    <h6 class="mb-3">📅 今日消息统计</h6>
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="h4 text-primary">{{ messageStats.today.total }}</div>
                                            <small>今日总数</small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="h4 text-success">{{ messageStats.today.success }}</div>
                                            <small>今日成功</small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="h4 text-danger">{{ messageStats.today.failed }}</div>
                                            <small>今日失败</small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="h4 text-info">{{ messageStats.today.success_rate }}%</div>
                                            <small>今日成功率</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消息列表 -->
                    <div class="tab-pane fade" id="list" role="tabpanel">
                        <!-- 搜索和筛选 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" v-model="messageFilter.type" @change="loadMessages">
                                    <option value="">全部类型</option>
                                    <option value="wechat">微信消息</option>
                                    <option value="sms">短信消息</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" v-model="messageFilter.status" @change="loadMessages">
                                    <option value="">全部状态</option>
                                    <option value="0">待发送</option>
                                    <option value="1">发送成功</option>
                                    <option value="2">发送失败</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="搜索用户ID或内容" v-model="messageFilter.keyword" @keyup.enter="loadMessages">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" @click="loadMessages" :disabled="loading">
                                    <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                                    🔍 搜索
                                </button>
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div v-if="loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载消息列表...</p>
                        </div>

                        <!-- 空状态 -->
                        <div v-else-if="messages.length === 0" class="empty-state">
                            <div class="icon">📭</div>
                            <h5>暂无消息记录</h5>
                            <p class="text-muted">当前筛选条件下没有找到消息记录</p>
                        </div>

                        <!-- 消息列表表格 -->
                        <div v-else class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>用户</th>
                                        <th>类型</th>
                                        <th>模板ID</th>
                                        <th>内容</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="message in messages" :key="message.id">
                                        <td>{{ message.id }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ message.unionid.substring(0, 8) }}...</span>
                                        </td>
                                        <td>
                                            <span class="badge" :class="message.type === 'wechat' ? 'bg-success' : 'bg-info'">
                                                {{ message.type === 'wechat' ? '💬 微信' : '📱 企微微信' }}
                                            </span>
                                        </td>
                                        <td>{{ message.template_id || '-' }}</td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" :title="message.content">
                                                {{ message.content }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge" :class="getStatusClass(message.status)">
                                                {{ getStatusText(message.status) }}
                                            </span>
                                        </td>
                                        <td>{{ formatDate(message.created_at) }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" @click="viewMessageDetail(message)" data-bs-toggle="modal" data-bs-target="#messageDetailModal">
                                                👁️ 详情
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav v-if="pagination.total > 0" class="mt-3">
                            <ul class="pagination justify-content-center">
                                <li class="page-item" :class="{ disabled: pagination.current_page <= 1 }">
                                    <a class="page-link" href="#" @click.prevent="changePage(pagination.current_page - 1)">上一页</a>
                                </li>
                                <li class="page-item" v-for="page in getPageNumbers()" :key="page" :class="{ active: page === pagination.current_page }">
                                    <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                </li>
                                <li class="page-item" :class="{ disabled: pagination.current_page >= pagination.last_page }">
                                    <a class="page-link" href="#" @click.prevent="changePage(pagination.current_page + 1)">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>

                    <!-- 发送测试 -->
                    <div class="tab-pane fade" id="test" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="test-form">
                                    <h6 class="mb-3">💬 微信模板消息测试</h6>
                                    <form @submit.prevent="sendWechatTest">
                                        <div class="mb-3">
                                            <label class="form-label">接收用户OpenID</label>
                                            <input type="text" class="form-control" v-model="wechatTest.touser" placeholder="用户的OpenID" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模板ID</label>
                                            <input type="text" class="form-control" v-model="wechatTest.template_id" placeholder="微信模板消息ID" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">跳转链接</label>
                                            <input type="url" class="form-control" v-model="wechatTest.url" placeholder="点击模板消息后的跳转链接">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模板数据 (JSON格式)</label>
                                            <textarea class="form-control" rows="6" v-model="wechatTest.data" placeholder='{"character_string7": {"value": "ORD202501110001"}, "phrase3": {"value": "待审核"}, "time6": {"value": "2025-01-11 14:30:25"}}'></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-success w-100" :disabled="wechatTestLoading">
                                            <span v-if="wechatTestLoading" class="spinner-border spinner-border-sm me-2"></span>
                                            📤 发送微信测试消息
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="test-form">
                                    <h6 class="mb-3">🏢 企业微信消息测试</h6>
                                    <form @submit.prevent="sendWorkTest">
                                        <div class="mb-3">
                                            <label class="form-label">接收用户</label>
                                            <input type="text" class="form-control" v-model="workTest.touser" placeholder="用户ID或@all" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">消息类型</label>
                                            <select class="form-select" v-model="workTest.msgtype">
                                                <option value="text">文本消息</option>
                                                <option value="markdown">Markdown消息</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">消息内容</label>
                                            <textarea class="form-control" rows="6" v-model="workTest.message" placeholder="请输入要发送的消息内容" required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-info w-100" :disabled="workTestLoading">
                                            <span v-if="workTestLoading" class="spinner-border spinner-border-sm me-2"></span>
                                            📤 发送企业微信测试消息
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果 -->
                        <div class="row mt-4" v-if="testResults.length > 0">
                            <div class="col-12">
                                <div class="stat-card">
                                    <h6 class="mb-3">📋 测试结果</h6>
                                    <div v-for="(result, index) in testResults" :key="index" class="test-result" :class="result.success ? 'success' : 'error'">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <strong>{{ result.type }}</strong> - {{ result.time }}
                                                <br>
                                                <span>{{ result.message }}</span>
                                                <div v-if="result.data" class="mt-2">
                                                    <small class="text-muted">响应数据:</small>
                                                    <pre class="mt-1 mb-0"><code>{{ JSON.stringify(result.data, null, 2) }}</code></pre>
                                                </div>
                                            </div>
                                            <button class="btn btn-sm btn-outline-secondary" @click="testResults.splice(index, 1)">
                                                ✕
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息详情模态框 -->
        <div class="modal fade" id="messageDetailModal" tabindex="-1" aria-labelledby="messageDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="messageDetailModalLabel">
                            <span class="me-2">📧</span>消息详情
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" v-if="selectedMessage">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>消息ID:</strong></td>
                                        <td>{{ selectedMessage.id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>用户ID:</strong></td>
                                        <td>{{ selectedMessage.unionid }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>消息类型:</strong></td>
                                        <td>
                                            <span class="badge" :class="selectedMessage.type === 'wechat' ? 'bg-success' : 'bg-info'">
                                                {{ selectedMessage.type === 'wechat' ? '💬 微信' : '📱 短信' }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>模板ID:</strong></td>
                                        <td>{{ selectedMessage.template_id || '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态:</strong></td>
                                        <td>
                                            <span class="badge" :class="getStatusClass(selectedMessage.status)">
                                                {{ getStatusText(selectedMessage.status) }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间:</strong></td>
                                        <td>{{ selectedMessage.created_at }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>消息内容</h6>
                                <div class="border rounded p-3 bg-light">
                                    <pre>{{ selectedMessage.content }}</pre>
                                </div>

                                <h6 class="mt-3">响应信息</h6>
                                <div class="border rounded p-3 bg-light">
                                    <pre>{{ selectedMessage.response || '暂无响应信息' }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <p>&copy; 2025 中科生活消息平台. All rights reserved.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/common/bootstrap-5.3.0-dist/js/bootstrap.bundle.min.js"></script>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    loading: false,
                    // 消息统计数据
                    messageStats: {
                        total: 0,
                        success: 0,
                        failed: 0,
                        pending: 0,
                        wechat: { total: 0, success: 0, success_rate: 0 },
                        sms: { total: 0, success: 0, success_rate: 0 },
                        today: { total: 0, success: 0, failed: 0, success_rate: 0 }
                    },
                    // 消息列表
                    messages: [],
                    messageFilter: {
                        type: '',
                        status: '',
                        keyword: ''
                    },
                    pagination: {
                        current_page: 1,
                        last_page: 1,
                        per_page: 10,
                        total: 0
                    },
                    selectedMessage: null,
                    // 测试相关数据
                    wechatTest: {
                        touser: '',
                        template_id: '',
                        url: 'https://img.zkshlm.com/zksh/error.html?title=太空实验室文昌基地消息通知&content=订单待审核',
                        data: '{\n  "character_string7": {"value": "ORD202501110001"},\n  "phrase3": {"value": "待审核"},\n  "time6": {"value": "2025-01-11 14:30:25"}\n}'
                    },
                    workTest: {
                        touser: '@all',
                        msgtype: 'text',
                        message: '这是一条来自系统的测试消息，用于验证企业微信配置是否正确。\n\n发送时间：' + new Date().toLocaleString()
                    },
                    wechatTestLoading: false,
                    workTestLoading: false,
                    testResults: []
                }
            },
            mounted() {
                this.loadMessageStats();
            },
            methods: {
                // 加载消息统计
                async loadMessageStats() {
                    try {
                        const response = await axios.get('/api/messageStats', { params: { token: 'zksh62576563' } });
                        if (response.data.code === 0) {
                            this.messageStats = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载消息统计失败:', error);
                    }
                },

                // 加载消息列表
                async loadMessages() {
                    this.loading = true;
                    try {
                        const page = this.pagination.current_page;
                        const params = {
                            page: page,
                            per_page: this.pagination.per_page,
                            type: this.messageFilter.type,
                            status: this.messageFilter.status,
                            keyword: this.messageFilter.keyword,
                            token: 'zksh62576563'
                        };

                        const response = await axios.get('/api/messages', { params });
                        if (response.data.code === 0) {
                            this.messages = response.data.data.data;
                            this.pagination = response.data.data.pagination;
                        }
                    } catch (error) {
                        console.error('加载消息列表失败:', error);
                        this.messages = [];
                    } finally {
                        this.loading = false;
                    }
                },

                // 查看消息详情
                viewMessageDetail(message) {
                    this.selectedMessage = message;
                },

                // 发送微信测试消息
                async sendWechatTest() {
                    this.wechatTestLoading = true;
                    try {
                        let templateData;
                        try {
                            templateData = JSON.parse(this.wechatTest.data);
                        } catch (e) {
                            throw new Error('模板数据JSON格式错误');
                        }

                        const testData = {
                            code: 'dsd45d4a6d78w7d8ad749a7d7af4a4ga54f6as4dsa4g41a51sd2adw4d8s8cx4512c31g45',
                            data: {
                                touser: this.wechatTest.touser,
                                template_id: this.wechatTest.template_id,
                                url: this.wechatTest.url,
                                data: templateData
                            }
                        };

                        const response = await axios.post('/api/reserveSend', testData);

                        this.testResults.unshift({
                            type: '微信模板消息',
                            time: new Date().toLocaleString(),
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        });

                        if (response.data.code === 0) {
                            alert('微信测试消息发送成功！');
                        } else {
                            alert('微信测试消息发送失败：' + response.data.message);
                        }
                    } catch (error) {
                        this.testResults.unshift({
                            type: '微信模板消息',
                            time: new Date().toLocaleString(),
                            success: false,
                            message: error.message || '发送失败',
                            data: null
                        });
                        alert('微信测试消息发送失败：' + (error.message || '网络错误'));
                    } finally {
                        this.wechatTestLoading = false;
                    }
                },

                // 发送企业微信测试消息
                async sendWorkTest() {
                    this.workTestLoading = true;
                    try {
                        const response = await axios.post('/api/wechatWorkSendMessage', {
                            touser: this.workTest.touser,
                            message: this.workTest.message,
                            msgtype: this.workTest.msgtype,
                            token: 'zksh62576563'
                        });

                        this.testResults.unshift({
                            type: '企业微信消息',
                            time: new Date().toLocaleString(),
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        });

                        if (response.data.code === 0) {
                            alert('企业微信测试消息发送成功！');
                        } else {
                            alert('企业微信测试消息发送失败：' + response.data.message);
                        }
                    } catch (error) {
                        this.testResults.unshift({
                            type: '企业微信消息',
                            time: new Date().toLocaleString(),
                            success: false,
                            message: error.message || '发送失败',
                            data: null
                        });
                        alert('企业微信测试消息发送失败：' + (error.message || '网络错误'));
                    } finally {
                        this.workTestLoading = false;
                    }
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.pagination.last_page) {
                        this.loadMessages(page);
                    }
                },

                // 获取分页页码
                getPageNumbers() {
                    const pages = [];
                    const current = this.pagination.current_page;
                    const last = this.pagination.last_page;

                    let start = Math.max(1, current - 2);
                    let end = Math.min(last, current + 2);

                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }

                    return pages;
                },

                // 获取状态样式类
                getStatusClass(status) {
                    switch (parseInt(status)) {
                        case 0: return 'bg-warning';
                        case 1: return 'bg-success';
                        case 2: return 'bg-danger';
                        default: return 'bg-secondary';
                    }
                },

                // 获取状态文本
                getStatusText(status) {
                    switch (parseInt(status)) {
                        case 0: return '⏰ 待发送';
                        case 1: return '✅ 发送成功';
                        case 2: return '❌ 发送失败';
                        default: return '❓ 未知状态';
                    }
                },

                // 格式化日期
                formatDate(dateString) {
                    if (!dateString) return '-';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN');
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
