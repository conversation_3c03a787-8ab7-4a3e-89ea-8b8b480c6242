<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API日志仪表盘 - 中科生活消息平台</title>

    <!-- Bootstrap CSS -->
    <link href="/common/bootstrap-5.3.0-dist/css/bootstrap.css" rel="stylesheet">

    <!-- Vue 3 -->
    <script src="/common/vue.global.js"></script>

    <!-- Axios -->
    <script src="/common/axios.min.js"></script>

    <!-- Chart.js -->
    <script src="/common/chart.js"></script>
    <script>
        // 如果本地Chart.js加载失败，使用CDN备用
        if (typeof Chart === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/chart.js"><\/script>');
        }
    </script>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .stats-detail {
            font-size: 0.8rem;
            color: #28a745;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        #trendChart {
            display: block;
            width: 800px;
            height: 400px;
            border: 1px solid #ddd;
            background-color: #fff;
        }

        /* 消息提示样式 */
        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-in-out;
        }

        .message-modal {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-width: 300px;
            max-width: 500px;
            animation: slideIn 0.3s ease-out;
        }

        .message-icon {
            font-size: 2rem;
            flex-shrink: 0;
        }

        .message-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .message-success {
            border-left: 5px solid #28a745;
        }

        .message-warning {
            border-left: 5px solid #ffc107;
        }

        .message-danger {
            border-left: 5px solid #dc3545;
        }

        .message-info {
            border-left: 5px solid #17a2b8;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .table-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .method-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .method-post {
            background-color: #28a745;
            color: white;
        }
        
        .method-get {
            background-color: #007bff;
            color: white;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        .back-btn {
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .back-btn:hover {
            color: #f8f9fa;
            text-decoration: none;
        }
        
        .test-form {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .form-control {
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 6px;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            border-radius: 6px;
        }
        
        .response-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="container">
                <a href="/" class="back-btn">
                    <span style="font-size: 1.2rem; margin-right: 0.5rem;">←</span>
                    返回首页
                </a>
                <h1><span class="me-3">📊</span>API日志仪表盘</h1>
                <p class="mb-0">API接口调用统计和日志监控</p>
            </div>
        </div>

        <div class="container">
            <!-- 统计卡片 -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-primary">{{ stats.summary.total_requests }}</div>
                        <div class="stats-label">总请求数</div>
                        <div class="stats-detail">今日: {{ stats.summary.today_requests }}</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-success">{{ stats.summary.success_rate }}%</div>
                        <div class="stats-label">成功率</div>
                        <div class="stats-detail">今日: {{ stats.summary.today_success_rate }}%</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-info">{{ stats.summary.avg_duration }}ms</div>
                        <div class="stats-label">平均响应时间</div>
                        <div class="stats-detail">系统性能指标</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-warning">{{ stats.summary.failed_requests }}</div>
                        <div class="stats-label">失败请求</div>
                        <div class="stats-detail">今日: {{ stats.summary.today_failed_requests }}</div>
                    </div>
                </div>
            </div>

            <!-- 请求趋势图表 -->
            <div class="chart-container">
                <h5 class="mb-3">📈 最近7天请求趋势</h5>
                <div style="position: relative; height: 300px;">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>

            <!-- 接口统计和状态码统计 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="table-container">
                        <h5 class="mb-3">🔥 热门接口TOP10</h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>接口</th>
                                        <th>调用次数</th>
                                        <th>平均耗时</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item in stats.endpoint_stats" :key="item.endpoint">
                                        <td><code>{{ item.endpoint }}</code></td>
                                        <td><span class="badge bg-primary">{{ item.count }}</span></td>
                                        <td>{{ Math.round(item.avg_duration) }}ms</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="table-container">
                        <h5 class="mb-3">📊 状态码统计</h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>状态码</th>
                                        <th>次数</th>
                                        <th>占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item in stats.status_stats" :key="item.status_code">
                                        <td>
                                            <span :class="getStatusBadgeClass(item.status_code)">
                                                {{ item.status_code }}
                                            </span>
                                        </td>
                                        <td>{{ item.count }}</td>
                                        <td>{{ getStatusPercentage(item.count) }}%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Token配置 -->
            <div class="table-container">
                <h5 class="mb-3">🔑 API Token配置</h5>
                <p class="text-muted">所有API接口都需要在请求头中携带Authorization Token或在参数中传递token</p>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">API Token</label>
                            <input type="text" class="form-control" v-model="apiToken" placeholder="请输入API访问令牌">
                        </div>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button class="btn btn-success" @click="saveApiToken" :disabled="loading.saveToken">
                            <span v-if="loading.saveToken" class="spinner-border spinner-border-sm me-1"></span>
                            保存Token
                        </button>
                    </div>
                </div>
            </div>

            <!-- API日志表格 -->
            <div class="table-container">
                <h5 class="mb-3">📋 API请求日志</h5>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="date" class="form-control" v-model="filters.date" @change="loadLogs">
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" v-model="filters.endpoint" @change="loadLogs">
                            <option value="">所有接口</option>
                            <option v-for="endpoint in endpointOptions" :key="endpoint" :value="endpoint">{{ endpoint }}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" v-model="filters.status_code" @change="loadLogs">
                            <option value="">所有状态</option>
                            <option value="200">成功(200)</option>
                            <option value="401">未授权(401)</option>
                            <option value="500">服务器错误(500)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" @click="loadLogs">
                            <span v-if="loading.logs" class="spinner-border spinner-border-sm me-1"></span>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>接口</th>
                                <th>方法</th>
                                <th>IP地址</th>
                                <th>状态码</th>
                                <th>耗时</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="log in logs.logs" :key="log.id">
                                <td>{{ formatTime(log.created_at) }}</td>
                                <td><code>{{ log.endpoint }}</code></td>
                                <td><span class="badge bg-info">{{ log.method }}</span></td>
                                <td>{{ log.ip }}</td>
                                <td>
                                    <span :class="getStatusBadgeClass(log.status_code)">
                                        {{ log.status_code }}
                                    </span>
                                </td>
                                <td>{{ log.duration }}ms</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" @click="showLogDetail(log)">
                                        详情
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav v-if="logs.total > 0">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" :class="{ disabled: logs.page <= 1 }">
                            <button class="page-link" @click="changePage(logs.page - 1)" :disabled="logs.page <= 1">上一页</button>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">{{ logs.page }} / {{ Math.ceil(logs.total / logs.limit) }}</span>
                        </li>
                        <li class="page-item" :class="{ disabled: logs.page >= Math.ceil(logs.total / logs.limit) }">
                            <button class="page-link" @click="changePage(logs.page + 1)" :disabled="logs.page >= Math.ceil(logs.total / logs.limit)">下一页</button>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 日志详情模态框 -->
            <div class="modal fade" id="logDetailModal" tabindex="-1" v-if="selectedLog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">API请求详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>接口:</strong> {{ selectedLog.endpoint }}
                                </div>
                                <div class="col-md-6">
                                    <strong>方法:</strong> {{ selectedLog.method }}
                                </div>
                                <div class="col-md-6">
                                    <strong>IP地址:</strong> {{ selectedLog.ip }}
                                </div>
                                <div class="col-md-6">
                                    <strong>状态码:</strong> {{ selectedLog.status_code }}
                                </div>
                                <div class="col-md-6">
                                    <strong>耗时:</strong> {{ selectedLog.duration }}ms
                                </div>
                                <div class="col-md-6">
                                    <strong>时间:</strong> {{ selectedLog.created_at }}
                                </div>
                            </div>
                            <hr>
                            <div class="mb-3">
                                <strong>User Agent:</strong>
                                <pre class="bg-light p-2 rounded">{{ selectedLog.user_agent }}</pre>
                            </div>
                            <div class="mb-3">
                                <strong>请求参数:</strong>
                                <pre class="bg-light p-2 rounded">{{ formatJson(selectedLog.params) }}</pre>
                            </div>
                            <div class="mb-3">
                                <strong>响应内容:</strong>
                                <pre class="bg-light p-2 rounded">{{ formatJson(selectedLog.response) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息提示 -->
            <div v-if="message.show" class="message-overlay">
                <div class="message-modal" :class="'message-' + message.type">
                    <div class="message-icon">
                        <span v-if="message.type === 'success'">✅</span>
                        <span v-else-if="message.type === 'warning'">⚠️</span>
                        <span v-else-if="message.type === 'danger'">❌</span>
                        <span v-else>ℹ️</span>
                    </div>
                    <div class="message-text">{{ message.text }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/common/bootstrap-5.3.0-dist/js/bootstrap.bundle.js"></script>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    apiToken: '',
                    stats: {
                        summary: {
                            total_requests: 0,
                            today_requests: 0,
                            success_requests: 0,
                            today_success_requests: 0,
                            failed_requests: 0,
                            today_failed_requests: 0,
                            success_rate: 0,
                            today_success_rate: 0,
                            avg_duration: 0
                        },
                        endpoint_stats: [],
                        status_stats: [],
                        trend_data: []
                    },
                    logs: {
                        data: [],
                        total: 0,
                        page: 1,
                        limit: 20
                    },
                    filters: {
                        date: '',
                        endpoint: '',
                        status_code: ''
                    },
                    endpointOptions: [],
                    selectedLog: null,
                    loading: {
                        saveToken: false,
                        stats: false,
                        logs: false
                    },
                    message: {
                        show: false,
                        type: 'success',
                        text: ''
                    },
                    trendChart: null
                }
            },
            mounted() {
                this.loadApiToken().then(() => {
                    // Token加载完成后再加载其他数据
                    this.loadStats();
                    this.loadLogs();
                });

                // 延迟初始化图表，确保DOM完全加载
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.initChart();
                    }, 100);
                });
            },
            methods: {
                // 加载API Token
                async loadApiToken() {
                    try {
                        const response = await axios.get('/setting/get');
                        if (response.data && response.data.code === 0) {
                            const settings = response.data.data;
                            this.apiToken = settings.api_token?.value || '';
                        }
                    } catch (error) {
                        console.error('加载API Token失败:', error);
                    }
                },

                // 保存API Token
                async saveApiToken() {
                    this.loading.saveToken = true;

                    try {
                        const response = await axios.post('/setting/save', {
                            settings: { api_token: this.apiToken }
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage('API Token保存成功', 'success');
                        } else {
                            this.showMessage(response.data.message || '保存失败', 'danger');
                        }
                    } catch (error) {
                        console.error('保存API Token失败:', error);
                        this.showMessage('保存API Token失败', 'danger');
                    } finally {
                        this.loading.saveToken = false;
                    }
                },

                // 加载统计数据
                async loadStats() {
                    if (!this.apiToken) {
                        console.warn('API Token未加载，跳过统计数据加载');
                        return;
                    }

                    console.log('开始加载统计数据，Token:', this.apiToken);
                    this.loading.stats = true;

                    try {
                        const response = await axios.get('/api/getStats', {
                            params: { token: this.apiToken }
                        });

                        console.log('统计数据响应:', response.data);

                        if (response.data && response.data.code === 0) {
                            this.stats = response.data.data;
                            console.log('统计数据已更新:', this.stats);
                            // 延迟更新图表，避免响应式数据冲突
                            this.$nextTick(() => {
                                this.updateChart();
                            });
                        } else {
                            console.error('统计数据响应错误:', response.data);
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                    } finally {
                        this.loading.stats = false;
                    }
                },

                // 加载日志数据
                async loadLogs() {
                    if (!this.apiToken) {
                        console.warn('API Token未加载，跳过日志数据加载');
                        return;
                    }

                    console.log('开始加载日志数据，Token:', this.apiToken);
                    this.loading.logs = true;

                    try {
                        const params = {
                            page: this.logs.page,
                            limit: this.logs.limit,
                            token: this.apiToken
                        };

                        if (this.filters.date) params.date = this.filters.date;
                        if (this.filters.endpoint) params.endpoint = this.filters.endpoint;
                        if (this.filters.status_code) params.status_code = this.filters.status_code;

                        console.log('请求参数:', params);
                        const response = await axios.get('/api/getLogs', { params });

                        console.log('日志数据响应:', response.data);

                        if (response.data && response.data.code === 0) {
                            this.logs = response.data.data;
                            console.log('日志数据已更新:', this.logs);

                            // 更新接口选项
                            if (this.logs.logs && Array.isArray(this.logs.logs)) {
                                const endpoints = [...new Set(this.logs.logs.map(log => log.endpoint))];
                                this.endpointOptions = endpoints;
                            }
                        } else {
                            console.error('日志数据响应错误:', response.data);
                        }
                    } catch (error) {
                        console.error('加载日志失败:', error);
                    } finally {
                        this.loading.logs = false;
                    }
                },

                // 初始化图表
                initChart() {
                    try {
                        console.log('开始初始化图表...');

                        // 检查Chart.js是否加载
                        if (typeof Chart === 'undefined') {
                            console.error('Chart.js未加载');
                            return;
                        }

                        const canvas = document.getElementById('trendChart');
                        if (!canvas) {
                            console.error('找不到canvas元素');
                            return;
                        }

                        const ctx = canvas.getContext('2d');
                        // 使用markRaw标记Chart实例为非响应式，避免Vue3响应式冲突
                        this.trendChart = Vue.markRaw(new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: [],
                                datasets: [{
                                    label: '请求数量',
                                    data: [],
                                    borderColor: 'rgb(75, 192, 192)',
                                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                    tension: 0.1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        }));

                        console.log('图表初始化成功');
                    } catch (error) {
                        console.error('图表初始化失败:', error);
                    }
                },

                // 更新图表
                updateChart() {
                    try {
                        console.log('开始更新图表...');

                        if (!this.trendChart) {
                            console.warn('图表未初始化，尝试重新初始化');
                            this.initChart();
                            return;
                        }

                        if (!this.stats.trend_data) {
                            console.warn('趋势数据为空');
                            return;
                        }

                        // 使用JSON.parse(JSON.stringify())创建非响应式副本
                        const trendData = JSON.parse(JSON.stringify(this.stats.trend_data));
                        console.log('趋势数据:', trendData);

                        this.trendChart.data.labels = trendData.map(item => item.date);
                        this.trendChart.data.datasets[0].data = trendData.map(item => item.count);
                        this.trendChart.update();

                        console.log('图表更新成功');
                    } catch (error) {
                        console.error('图表更新失败:', error);
                    }
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= Math.ceil(this.logs.total / this.logs.limit)) {
                        this.logs.page = page;
                        this.loadLogs();
                    }
                },

                // 显示日志详情
                showLogDetail(log) {
                    this.selectedLog = log;
                    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
                    modal.show();
                },

                // 格式化时间
                formatTime(time) {
                    return new Date(time).toLocaleString();
                },

                // 格式化JSON
                formatJson(jsonStr) {
                    try {
                        return JSON.stringify(JSON.parse(jsonStr), null, 2);
                    } catch (e) {
                        return jsonStr;
                    }
                },

                // 获取状态码样式
                getStatusBadgeClass(statusCode) {
                    if (statusCode >= 200 && statusCode < 300) {
                        return 'badge bg-success';
                    } else if (statusCode >= 400 && statusCode < 500) {
                        return 'badge bg-warning';
                    } else if (statusCode >= 500) {
                        return 'badge bg-danger';
                    }
                    return 'badge bg-secondary';
                },

                // 获取状态码占比
                getStatusPercentage(count) {
                    const total = this.stats.summary.total_requests;
                    return total > 0 ? ((count / total) * 100).toFixed(1) : 0;
                },

                // 显示消息
                showMessage(message, type) {
                    this.message.show = true;
                    this.message.type = type;
                    this.message.text = message;

                    setTimeout(() => {
                        this.message.show = false;
                    }, 3000);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
