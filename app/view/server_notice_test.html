<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server酱通知测试 - AI增强版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app" class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🤖 Server酱通知测试 - AI增强版</h5>
                    </div>
                    <div class="card-body">
                        <form @submit.prevent="sendNotice">
                            <div class="mb-3">
                                <label for="title" class="form-label">通知标题 *</label>
                                <input type="text" class="form-control" id="title" v-model="form.title" 
                                       placeholder="例如：服务器告警" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="content" class="form-label">通知内容 *</label>
                                <textarea class="form-control" id="content" rows="4" v-model="form.content" 
                                          placeholder="例如：CPU使用率超过90%，请及时处理！" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">服务名称</label>
                                <input type="text" class="form-control" readonly
                                       :value="serviceName"
                                       placeholder="从环境变量APP_NAME获取">
                                <small class="form-text text-muted">服务名称自动从环境变量APP_NAME获取</small>
                            </div>
                            

                            
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-robot me-2"></i>
                                <strong>AI优化已启用</strong> - 系统将自动使用AI优化消息内容，提高可读性和专业性
                            </div>
                            
                            <button type="submit" class="btn btn-primary" :disabled="loading">
                                <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                                {{ loading ? '发送中...' : '发送通知' }}
                            </button>
                            
                            <button type="button" class="btn btn-outline-info ms-2" @click="loadStats">
                                📊 查看统计
                            </button>
                        </form>
                        
                        <!-- 结果显示 -->
                        <div v-if="result" class="mt-4">
                            <div class="alert" :class="result.success ? 'alert-success' : 'alert-danger'">
                                <h6>{{ result.success ? '✅ 发送成功' : '❌ 发送失败' }}</h6>
                                <p class="mb-0">{{ result.message }}</p>
                                <div v-if="result.data" class="mt-2">
                                    <small class="text-muted">
                                        AI优化: {{ result.data.ai_enabled ? '已启用' : '未启用' }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📈 消息统计</h6>
                    </div>
                    <div class="card-body">
                        <div v-if="stats">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2 mb-2">
                                        <div class="h5 mb-0 text-primary">{{ stats.total }}</div>
                                        <small class="text-muted">总消息</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 mb-2">
                                        <div class="h5 mb-0 text-success">{{ stats.success }}</div>
                                        <small class="text-muted">成功</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 mb-2">
                                        <div class="h5 mb-0 text-danger">{{ stats.failed }}</div>
                                        <small class="text-muted">失败</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 mb-2">
                                        <div class="h5 mb-0 text-warning">{{ stats.pending }}</div>
                                        <small class="text-muted">待处理</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="d-flex justify-content-between">
                                    <span>成功率</span>
                                    <span class="fw-bold text-success">{{ stats.success_rate }}%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" :style="{width: stats.success_rate + '%'}"></div>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <small class="text-muted">今日消息: {{ stats.today }}</small>
                            </div>
                        </div>
                        <div v-else class="text-center text-muted">
                            <small>点击"查看统计"加载数据</small>
                        </div>
                    </div>
                </div>
                
                <!-- 预设模板 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">📝 快速模板</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-warning btn-sm" @click="useTemplate('cpu')">
                                🔥 CPU告警
                            </button>
                            <button class="btn btn-outline-danger btn-sm" @click="useTemplate('memory')">
                                💾 内存告警
                            </button>
                            <button class="btn btn-outline-info btn-sm" @click="useTemplate('disk')">
                                💿 磁盘告警
                            </button>
                            <button class="btn btn-outline-success btn-sm" @click="useTemplate('service')">
                                🔧 服务异常
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    form: {
                        title: '',
                        content: '',
                        token:'zksh62576563'
                    },
                    serviceName: 'Unknown Service', // 显示用的服务名
                    loading: false,
                    result: null,
                    stats: null,
                    templates: {
                        cpu: {
                            title: '服务器CPU告警',
                            content: 'CPU使用率超过90%，当前使用率95%，请及时处理！'
                        },
                        memory: {
                            title: '服务器内存告警',
                            content: '内存使用率超过85%，当前使用率92%，可用内存不足1GB'
                        },
                        disk: {
                            title: '磁盘空间告警',
                            content: '磁盘使用率超过90%，/var分区剩余空间不足2GB'
                        },
                        service: {
                            title: '服务异常告警',
                            content: 'Nginx服务异常停止，网站无法访问，请立即检查'
                        }
                    }
                }
            },
            mounted() {
                this.loadStats();
                this.loadServiceName();
            },
            methods: {
                async sendNotice() {
                    this.loading = true;
                    this.result = null;
                    
                    try {
                        const response = await axios.post('/api/sendServerNotice', this.form);
                        this.result = {
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        };
                        
                        if (this.result.success) {
                            // 发送成功后刷新统计
                            setTimeout(() => {
                                this.loadStats();
                            }, 1000);
                        }
                    } catch (error) {
                        this.result = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                    } finally {
                        this.loading = false;
                    }
                },

                async loadServiceName() {
                    try {
                        // 可以通过API获取服务名称，这里先用默认值
                        this.serviceName = 'ZK Notice API'; // 可以从后端API获取
                    } catch (error) {
                        console.error('获取服务名称失败:', error);
                        this.serviceName = 'Unknown Service';
                    }
                },

                async loadStats() {
                    try {
                        const response = await axios.post('/api/getServerNoticeStats');
                        if (response.data.code === 0) {
                            this.stats = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载统计失败:', error);
                    }
                },
                
                useTemplate(type) {
                    const template = this.templates[type];
                    if (template) {
                        Object.assign(this.form, template);
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
