<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <link href="/common/bootstrap-5.3.0-dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="/common/<EMAIL>"></script>
    <script src="/common/axios.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .upload-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #e3f2fd;
        }
        
        .file-list {
            margin-top: 2rem;
        }
        
        .file-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .file-icon {
            font-size: 2rem;
        }
        
        .progress {
            height: 8px;
            margin-top: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        /* 消息提示样式 */
        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .message-modal {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-width: 300px;
            max-width: 500px;
            animation: slideIn 0.3s ease-out;
        }
        
        .message-icon {
            font-size: 2rem;
            flex-shrink: 0;
        }
        
        .message-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .message-success {
            border-left: 5px solid #28a745;
        }
        
        .message-warning {
            border-left: 5px solid #ffc107;
        }
        
        .message-danger {
            border-left: 5px solid #dc3545;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { 
                transform: translateY(-50px);
                opacity: 0;
            }
            to { 
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="upload-container">
                <h2 class="text-center mb-4">📁 文件上传测试</h2>
                
                <!-- API Token 配置 -->
                <div class="mb-4">
                    <label class="form-label">API Token</label>
                    <div class="input-group">
                        <input type="text" class="form-control" v-model="apiToken" placeholder="请输入API Token">
                        <button class="btn btn-outline-secondary" @click="loadToken">加载Token</button>
                    </div>
                </div>
                
                <!-- 用户ID配置 -->
                <div class="mb-4">
                    <label class="form-label">用户UnionID（可选）</label>
                    <input type="text" class="form-control" v-model="unionid" placeholder="请输入用户UnionID">
                </div>
                
                <!-- 文件上传区域 -->
                <div class="upload-area" 
                     @click="selectFiles"
                     @dragover.prevent="handleDragOver"
                     @dragleave.prevent="handleDragLeave"
                     @drop.prevent="handleDrop"
                     :class="{ dragover: isDragOver }">
                    <div class="upload-icon">📤</div>
                    <h4>点击选择文件或拖拽文件到此处</h4>
                    <p class="text-muted">支持图片、文档、视频、音频等多种格式，单个文件最大50MB</p>
                    <input type="file" ref="fileInput" @change="handleFileSelect" multiple style="display: none;">
                </div>
                
                <!-- 文件列表 -->
                <div v-if="files.length > 0" class="file-list">
                    <h5>待上传文件</h5>
                    <div v-for="(file, index) in files" :key="index" class="file-item">
                        <div class="file-info">
                            <div class="file-icon">{{ getFileIcon(file.type) }}</div>
                            <div>
                                <div class="fw-bold">{{ file.name }}</div>
                                <div class="text-muted small">{{ formatFileSize(file.size) }}</div>
                                <div v-if="file.progress !== undefined" class="progress">
                                    <div class="progress-bar" :style="{ width: file.progress + '%' }"></div>
                                </div>
                                <div v-if="file.status" class="small" :class="getStatusClass(file.status)">
                                    {{ file.message }}
                                </div>
                            </div>
                        </div>
                        <div>
                            <button v-if="!file.uploading" class="btn btn-sm btn-outline-danger" @click="removeFile(index)">
                                删除
                            </button>
                            <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary" @click="uploadFiles" :disabled="uploading || files.length === 0">
                            <span v-if="uploading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                            {{ uploading ? '上传中...' : '开始上传' }}
                        </button>
                        <button class="btn btn-outline-secondary ms-2" @click="clearFiles" :disabled="uploading">
                            清空列表
                        </button>
                    </div>
                </div>
                
                <!-- 上传结果 -->
                <div v-if="uploadResults.length > 0" class="mt-4">
                    <h5>上传结果</h5>
                    <div v-for="(result, index) in uploadResults" :key="index" class="file-item">
                        <div class="file-info">
                            <div class="file-icon">{{ result.success ? '✅' : '❌' }}</div>
                            <div>
                                <div class="fw-bold">{{ result.fileName }}</div>
                                <div class="small" :class="result.success ? 'text-success' : 'text-danger'">
                                    {{ result.message }}
                                </div>
                                <div v-if="result.success && result.data" class="small text-muted">
                                    文件ID: {{ result.data.id }} | 
                                    大小: {{ formatFileSize(result.data.size) }} |
                                    {{ result.data.is_duplicate ? '秒传' : '新上传' }}
                                </div>
                            </div>
                        </div>
                        <div v-if="result.success && result.data">
                            <a :href="result.data.url" target="_blank" class="btn btn-sm btn-outline-primary">
                                查看文件
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 消息提示 -->
        <div v-if="message.show" class="message-overlay">
            <div class="message-modal" :class="'message-' + message.type">
                <div class="message-icon">
                    <span v-if="message.type === 'success'">✅</span>
                    <span v-else-if="message.type === 'warning'">⚠️</span>
                    <span v-else-if="message.type === 'danger'">❌</span>
                    <span v-else>ℹ️</span>
                </div>
                <div class="message-text">{{ message.text }}</div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    apiToken: '',
                    unionid: '',
                    files: [],
                    uploadResults: [],
                    uploading: false,
                    isDragOver: false,
                    message: {
                        show: false,
                        type: 'success',
                        text: ''
                    }
                }
            },
            mounted() {
                this.loadToken();
            },
            methods: {
                // 加载API Token
                loadToken() {
                    const token = localStorage.getItem('api_token');
                    if (token) {
                        this.apiToken = token;
                    }
                },
                
                // 选择文件
                selectFiles() {
                    this.$refs.fileInput.click();
                },
                
                // 处理文件选择
                handleFileSelect(event) {
                    const files = Array.from(event.target.files);
                    this.addFiles(files);
                },
                
                // 处理拖拽
                handleDragOver(event) {
                    this.isDragOver = true;
                },
                
                handleDragLeave(event) {
                    this.isDragOver = false;
                },
                
                handleDrop(event) {
                    this.isDragOver = false;
                    const files = Array.from(event.dataTransfer.files);
                    this.addFiles(files);
                },
                
                // 添加文件到列表
                addFiles(files) {
                    files.forEach(file => {
                        // 检查文件大小
                        if (file.size > 50 * 1024 * 1024) {
                            this.showMessage(`文件 ${file.name} 超过50MB限制`, 'warning');
                            return;
                        }
                        
                        // 检查是否已存在
                        const exists = this.files.some(f => f.name === file.name && f.size === file.size);
                        if (!exists) {
                            this.files.push({
                                file: file,
                                name: file.name,
                                size: file.size,
                                type: file.type,
                                uploading: false,
                                progress: undefined,
                                status: null,
                                message: ''
                            });
                        }
                    });
                },
                
                // 移除文件
                removeFile(index) {
                    this.files.splice(index, 1);
                },
                
                // 清空文件列表
                clearFiles() {
                    this.files = [];
                    this.uploadResults = [];
                },
                
                // 上传文件
                async uploadFiles() {
                    if (!this.apiToken) {
                        this.showMessage('请先配置API Token', 'warning');
                        return;
                    }
                    
                    this.uploading = true;
                    this.uploadResults = [];
                    
                    for (let i = 0; i < this.files.length; i++) {
                        const fileItem = this.files[i];
                        fileItem.uploading = true;
                        fileItem.progress = 0;
                        
                        try {
                            const formData = new FormData();
                            formData.append('file', fileItem.file);
                            if (this.unionid) {
                                formData.append('unionid', this.unionid);
                            }
                            
                            const response = await axios.post('/api/file/upload', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                },
                                params: {
                                    token: this.apiToken
                                },
                                onUploadProgress: (progressEvent) => {
                                    fileItem.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                                }
                            });
                            
                            if (response.data.code === 0) {
                                fileItem.status = 'success';
                                fileItem.message = response.data.message;
                                this.uploadResults.push({
                                    success: true,
                                    fileName: fileItem.name,
                                    message: response.data.message,
                                    data: response.data.data
                                });
                            } else {
                                fileItem.status = 'error';
                                fileItem.message = response.data.message;
                                this.uploadResults.push({
                                    success: false,
                                    fileName: fileItem.name,
                                    message: response.data.message
                                });
                            }
                        } catch (error) {
                            fileItem.status = 'error';
                            fileItem.message = '上传失败: ' + error.message;
                            this.uploadResults.push({
                                success: false,
                                fileName: fileItem.name,
                                message: '上传失败: ' + error.message
                            });
                        }
                        
                        fileItem.uploading = false;
                    }
                    
                    this.uploading = false;
                    
                    const successCount = this.uploadResults.filter(r => r.success).length;
                    const totalCount = this.uploadResults.length;
                    this.showMessage(`上传完成：成功 ${successCount}/${totalCount}`, 'success');
                },
                
                // 获取文件图标
                getFileIcon(mimeType) {
                    if (mimeType.startsWith('image/')) return '🖼️';
                    if (mimeType.startsWith('video/')) return '🎥';
                    if (mimeType.startsWith('audio/')) return '🎵';
                    if (mimeType.includes('pdf')) return '📄';
                    if (mimeType.includes('word')) return '📝';
                    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
                    if (mimeType.includes('text')) return '📃';
                    return '📁';
                },
                
                // 格式化文件大小
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },
                
                // 获取状态样式类
                getStatusClass(status) {
                    return {
                        'text-success': status === 'success',
                        'text-danger': status === 'error'
                    };
                },
                
                // 显示消息
                showMessage(message, type = 'success') {
                    this.message.show = true;
                    this.message.type = type;
                    this.message.text = message;
                    
                    setTimeout(() => {
                        this.message.show = false;
                    }, 3000);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
