<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 中科生活消息平台</title>

    <!-- Bootstrap CSS -->
    <link href="/common/bootstrap-5.3.0-dist/css/bootstrap.css" rel="stylesheet">

    <!-- Vue 3 -->
    <script src="/common/vue.global.js"></script>

    <!-- Axios -->
    <script src="/common/axios.min.js"></script>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .setting-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .setting-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .setting-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .setting-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        
        .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        
        .btn-outline-secondary {
            border-radius: 8px;
            border-color: #6c757d;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            border-radius: 8px;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .back-btn {
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .back-btn:hover {
            color: #f8f9fa;
            text-decoration: none;
        }

        /* 消息提示样式 */
        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-in-out;
        }

        .message-modal {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-width: 300px;
            max-width: 500px;
            animation: slideIn 0.3s ease-out;
        }

        .message-icon {
            font-size: 2rem;
            flex-shrink: 0;
        }

        .message-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .message-success {
            border-left: 5px solid #28a745;
        }

        .message-warning {
            border-left: 5px solid #ffc107;
        }

        .message-danger {
            border-left: 5px solid #dc3545;
        }

        .message-info {
            border-left: 5px solid #17a2b8;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="container">
                <a href="/" class="back-btn">
                    <span style="font-size: 1.2rem; margin-right: 0.5rem;">←</span>
                    返回首页
                </a>
                <h1><span class="me-3">⚙️</span>系统设置</h1>
                <p class="mb-0">配置微信开放平台、公众平台和短信服务参数</p>
            </div>
        </div>

        <div class="container">
            <!-- 系统基本设置 -->
            <div class="setting-card">
                <div class="setting-title">
                    <span class="setting-icon">🏠</span>
                    系统基本设置
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">系统名称</label>
                            <input type="text" class="form-control" v-model="settings.system_name" placeholder="请输入系统名称">
                        </div>
                    </div>
                </div>
            </div>

        <!-- 微信开放平台设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">🌐</span>
                微信开放平台设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">开放平台AppID</label>
                        <input type="text" class="form-control" v-model="settings.wechat_open_app_id" placeholder="请输入微信开放平台AppID">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">开放平台AppSecret</label>
                        <input type="password" class="form-control" v-model="settings.wechat_open_app_secret" placeholder="请输入微信开放平台AppSecret">
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testWechatOpen" :disabled="loading.wechatOpen">
                    <span v-if="loading.wechatOpen" class="spinner-border spinner-border-sm me-1"></span>
                    测试连接
                </button>
            </div>
        </div>

        <!-- 微信公众平台设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">📱</span>
                微信公众平台设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">公众号AppID</label>
                        <input type="text" class="form-control" v-model="settings.wechat_mp_app_id" placeholder="请输入微信公众号AppID">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">公众号AppSecret</label>
                        <input type="password" class="form-control" v-model="settings.wechat_mp_app_secret" placeholder="请输入微信公众号AppSecret">
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testWechatMp" :disabled="loading.wechatMp">
                    <span v-if="loading.wechatMp" class="spinner-border spinner-border-sm me-1"></span>
                    测试连接
                </button>
            </div>
        </div>

        <!-- 企业微信设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">🏢</span>
                企业微信设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">企业ID (CorpID)</label>
                        <input type="text" class="form-control" v-model="settings.wechat_work_corp_id" placeholder="请输入企业微信CorpID">
                        <small class="form-text text-muted">企业微信管理后台 -> 我的企业 -> 企业信息</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">应用Secret</label>
                        <input type="password" class="form-control" v-model="settings.wechat_work_secret" placeholder="请输入企业微信应用Secret">
                        <small class="form-text text-muted">企业微信管理后台 -> 应用管理 -> 自建应用</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">应用ID (AgentID)</label>
                        <input type="number" class="form-control" v-model="settings.wechat_work_agent_id" placeholder="请输入企业微信应用AgentID">
                        <small class="form-text text-muted">企业微信管理后台 -> 应用管理 -> 自建应用</small>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testWechatWork" :disabled="loading.wechatWork">
                    <span v-if="loading.wechatWork" class="spinner-border spinner-border-sm me-1"></span>
                    测试连接
                </button>
                <button type="button" class="btn btn-outline-info" @click="testWechatWorkMessage" :disabled="loading.wechatWorkMessage">
                    <span v-if="loading.wechatWorkMessage" class="spinner-border spinner-border-sm me-1"></span>
                    测试发送消息
                </button>
                <button type="button" class="btn btn-outline-success" @click="testWechatWorkMembers" :disabled="loading.wechatWorkMembers">
                    <span v-if="loading.wechatWorkMembers" class="spinner-border spinner-border-sm me-1"></span>
                    测试获取应用成员
                </button>
            </div>
        </div>

        <!-- AI服务设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">🤖</span>
                AI服务设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">AI API地址</label>
                        <input type="url" class="form-control" v-model="settings.ai_api_url" placeholder="请输入AI API地址">
                        <small class="form-text text-muted">例如：https://tbai.xin/v1/chat/completions</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">AI API密钥</label>
                        <input type="password" class="form-control" v-model="settings.ai_api_key" placeholder="请输入AI API密钥">
                        <small class="form-text text-muted">用于访问AI服务的API密钥</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">AI模型名称</label>
                        <input type="text" class="form-control" v-model="settings.ai_model" placeholder="请输入AI模型名称">
                        <small class="form-text text-muted">例如：gpt-4.1-mini</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">请求超时时间（秒）</label>
                        <input type="number" class="form-control" v-model="settings.ai_timeout" placeholder="30" min="10" max="120">
                        <small class="form-text text-muted">AI API请求超时时间，默认30秒</small>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testAI" :disabled="loading.ai">
                    <span v-if="loading.ai" class="spinner-border spinner-border-sm me-1"></span>
                    测试AI连接
                </button>
            </div>
        </div>

        <!-- 短信设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">📨</span>
                短信服务设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">AccessKey</label>
                        <input type="text" class="form-control" v-model="settings.sms_access_key" placeholder="请输入短信服务AccessKey">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">SecretKey</label>
                        <input type="password" class="form-control" v-model="settings.sms_secret_key" placeholder="请输入短信服务SecretKey">
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testSms" :disabled="loading.sms">
                    <span v-if="loading.sms" class="spinner-border spinner-border-sm me-1"></span>
                    测试连接
                </button>
            </div>
        </div>

        <!-- S3存储设置 -->
        <div class="setting-card">
            <div class="setting-title">
                <span class="setting-icon">☁️</span>
                S3存储设置
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">S3 Endpoint</label>
                        <input type="text" class="form-control" v-model="settings.s3_endpoint" placeholder="请输入S3服务端点">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">S3 Region</label>
                        <input type="text" class="form-control" v-model="settings.s3_region" placeholder="请输入S3区域">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Access Key ID</label>
                        <input type="text" class="form-control" v-model="settings.s3_access_key" placeholder="请输入S3 Access Key ID">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Secret Access Key</label>
                        <input type="password" class="form-control" v-model="settings.s3_secret_key" placeholder="请输入S3 Secret Access Key">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Bucket Name</label>
                        <input type="text" class="form-control" v-model="settings.s3_bucket" placeholder="请输入S3存储桶名称">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">CDN域名</label>
                        <input type="text" class="form-control" v-model="settings.s3_cdn_domain" placeholder="请输入CDN域名（可选）">
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" @click="testS3" :disabled="loading.s3">
                    <span v-if="loading.s3" class="spinner-border spinner-border-sm me-1"></span>
                    测试连接
                </button>
            </div>
        </div>

        <!-- 保存按钮 -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-success btn-lg" @click="saveSettings" :disabled="loading.save">
                <span v-if="loading.save" class="spinner-border spinner-border-sm me-1"></span>
                💾 保存所有设置
            </button>
        </div>

        <!-- 消息提示 -->
        <div v-if="message.show" class="message-overlay">
            <div class="message-modal" :class="'message-' + message.type">
                <div class="message-icon">
                    <span v-if="message.type === 'success'">✅</span>
                    <span v-else-if="message.type === 'warning'">⚠️</span>
                    <span v-else-if="message.type === 'danger'">❌</span>
                    <span v-else>ℹ️</span>
                </div>
                <div class="message-text">{{ message.text }}</div>
            </div>
        </div>
    </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/common/bootstrap-5.3.0-dist/js/bootstrap.bundle.js"></script>
    
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    settings: {
                        system_name: '',
                        wechat_open_app_id: '',
                        wechat_open_app_secret: '',
                        wechat_mp_app_id: '',
                        wechat_mp_app_secret: '',
                        wechat_work_corp_id: '',
                        wechat_work_secret: '',
                        wechat_work_agent_id: '',
                        ai_api_url: '',
                        ai_api_key: '',
                        ai_model: '',
                        ai_timeout: 30,
                        sms_access_key: '',
                        sms_secret_key: '',
                        s3_endpoint: '',
                        s3_region: '',
                        s3_access_key: '',
                        s3_secret_key: '',
                        s3_bucket: '',
                        s3_cdn_domain: ''
                    },
                    loading: {
                        save: false,
                        wechatOpen: false,
                        wechatMp: false,
                        wechatWork: false,
                        wechatWorkMessage: false,
                        wechatWorkMembers: false,
                        ai: false,
                        sms: false,
                        s3: false
                    },
                    message: {
                        show: false,
                        type: 'success',
                        text: ''
                    }
                }
            },
            mounted() {
                this.loadSettings();
            },
            methods: {
                // 加载设置数据
                async loadSettings() {
                    try {
                        const response = await axios.get('/setting/get');
                        if (response.data && response.data.code === 0) {
                            const data = response.data.data;

                            // 填充设置数据
                            Object.keys(this.settings).forEach(key => {
                                if (data[key]) {
                                    this.settings[key] = data[key].value || '';
                                }
                            });
                        }
                    } catch (error) {
                        console.error('加载设置失败:', error);
                        this.showMessage('加载设置失败', 'danger');
                    }
                },

                // 保存设置
                async saveSettings() {
                    this.loading.save = true;

                    try {
                        const response = await axios.post('/setting/save', {
                            settings: this.settings
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '保存失败', 'danger');
                        }
                    } catch (error) {
                        console.error('保存设置失败:', error);
                        this.showMessage('保存设置失败', 'danger');
                    } finally {
                        this.loading.save = false;
                    }
                },

                // 测试微信开放平台配置
                async testWechatOpen() {
                    this.loading.wechatOpen = true;

                    try {
                        const response = await axios.post('/setting/testWechatOpen', {
                            app_id: this.settings.wechat_open_app_id,
                            app_secret: this.settings.wechat_open_app_secret
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试微信开放平台配置失败:', error);
                        this.showMessage('测试微信开放平台配置失败', 'danger');
                    } finally {
                        this.loading.wechatOpen = false;
                    }
                },

                // 测试微信公众平台配置
                async testWechatMp() {
                    this.loading.wechatMp = true;

                    try {
                        const response = await axios.post('/setting/testWechatMp', {
                            app_id: this.settings.wechat_mp_app_id,
                            app_secret: this.settings.wechat_mp_app_secret
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试微信公众平台配置失败:', error);
                        this.showMessage('测试微信公众平台配置失败', 'danger');
                    } finally {
                        this.loading.wechatMp = false;
                    }
                },

                // 测试企业微信配置
                async testWechatWork() {
                    this.loading.wechatWork = true;

                    try {
                        const response = await axios.post('/setting/testWechatWork', {
                            corp_id: this.settings.wechat_work_corp_id,
                            secret: this.settings.wechat_work_secret,
                            agent_id: this.settings.wechat_work_agent_id
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试企业微信配置失败:', error);
                        this.showMessage('测试企业微信配置失败', 'danger');
                    } finally {
                        this.loading.wechatWork = false;
                    }
                },

                // 测试企业微信发送消息
                async testWechatWorkMessage() {
                    this.loading.wechatWorkMessage = true;

                    try {
                        const response = await axios.post('/setting/testWechatWorkMessage', {
                            corp_id: this.settings.wechat_work_corp_id,
                            secret: this.settings.wechat_work_secret,
                            agent_id: this.settings.wechat_work_agent_id
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试发送消息失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试企业微信发送消息失败:', error);
                        this.showMessage('测试企业微信发送消息失败', 'danger');
                    } finally {
                        this.loading.wechatWorkMessage = false;
                    }
                },

                // 测试企业微信获取应用成员
                async testWechatWorkMembers() {
                    this.loading.wechatWorkMembers = true;

                    try {
                        const response = await axios.post('/setting/testWechatWorkMembers', {
                            corp_id: this.settings.wechat_work_corp_id,
                            secret: this.settings.wechat_work_secret,
                            agent_id: this.settings.wechat_work_agent_id
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试获取应用成员失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试企业微信获取应用成员失败:', error);
                        this.showMessage('测试企业微信获取应用成员失败', 'danger');
                    } finally {
                        this.loading.wechatWorkMembers = false;
                    }
                },

                // 测试AI配置
                async testAI() {
                    this.loading.ai = true;

                    try {
                        const response = await axios.post('/setting/testAI', {
                            api_url: this.settings.ai_api_url,
                            api_key: this.settings.ai_api_key,
                            model: this.settings.ai_model,
                            timeout: this.settings.ai_timeout
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试AI连接失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试AI配置失败:', error);
                        this.showMessage('测试AI配置失败', 'danger');
                    } finally {
                        this.loading.ai = false;
                    }
                },

                // 测试短信配置
                async testSms() {
                    this.loading.sms = true;

                    try {
                        const response = await axios.post('/setting/testSms', {
                            access_key: this.settings.sms_access_key,
                            secret_key: this.settings.sms_secret_key
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试短信配置失败:', error);
                        this.showMessage('测试短信配置失败', 'danger');
                    } finally {
                        this.loading.sms = false;
                    }
                },

                // 测试S3配置
                async testS3() {
                    this.loading.s3 = true;

                    try {
                        const response = await axios.post('/setting/testS3', {
                            endpoint: this.settings.s3_endpoint,
                            region: this.settings.s3_region,
                            access_key: this.settings.s3_access_key,
                            secret_key: this.settings.s3_secret_key,
                            bucket: this.settings.s3_bucket
                        });

                        if (response.data && response.data.code === 0) {
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.message || '测试失败', 'warning');
                        }
                    } catch (error) {
                        console.error('测试S3配置失败:', error);
                        this.showMessage('测试S3配置失败', 'danger');
                    } finally {
                        this.loading.s3 = false;
                    }
                },

                // 显示消息
                showMessage(message, type) {
                    this.message.show = true;
                    this.message.type = type;
                    this.message.text = message;

                    // 3秒后自动隐藏
                    setTimeout(() => {
                        this.message.show = false;
                    }, 3000);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
