<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中科生活消息平台</title>

    <!-- Bootstrap CSS -->
    <link href="/common/bootstrap-5.3.0-dist/css/bootstrap.css" rel="stylesheet">

    <!-- 引入vue3 -->
    <script src="/common/vue.global.js"></script>

    <!-- Axios -->
    <script src="/common/axios.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-icon.users {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-icon.messages {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .stat-icon.success {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-icon.pending {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 1rem;
        }
        
        .stat-detail {
            font-size: 0.9rem;
            color: #95a5a6;
        }
        
        .nav-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
            cursor: pointer;
        }
        
        .nav-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .nav-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .nav-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .nav-desc {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div id="app" class="container main-container">
        <!-- 头部 -->
        <div class="header">
            <h1><span class="me-3" style="font-size: 2rem;">🔔</span>中科生活消息平台</h1>
            <p>统一消息通知管理系统</p>
        </div>

        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon users">
                        👥
                    </div>
                    <div class="stat-number">{{ stats.users.total }}</div>
                    <div class="stat-label">总用户数</div>
                    <div class="stat-detail">
                        活跃: {{ stats.users.active }} | 今日新增: {{ stats.users.today_new }}
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon messages">
                        📧
                    </div>
                    <div class="stat-number">{{ stats.messages.total }}</div>
                    <div class="stat-label">总消息数</div>
                    <div class="stat-detail">
                        今日发送: {{ stats.messages.today }}
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon success">
                        📈
                    </div>
                    <div class="stat-number">{{ stats.messages.success_rate }}%</div>
                    <div class="stat-label">发送成功率</div>
                    <div class="stat-detail">
                        成功: {{ stats.messages.success }} | 失败: {{ stats.messages.failed }}
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon pending">
                        ⏰
                    </div>
                    <div class="stat-number">{{ stats.messages.pending }}</div>
                    <div class="stat-label">待处理消息</div>
                    <div class="stat-detail">
                        队列中的消息
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能导航 -->
        <div class="row mt-4">
            <div class="col-lg-3 col-md-6">
                <div class="nav-card" onclick="showComingSoon('用户管理')">
                    <div class="nav-icon users">
                        👥
                    </div>
                    <div class="nav-title">用户管理</div>
                    <div class="nav-desc">管理微信用户信息和身份绑定</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <a href="/message" class="nav-card">
                    <div class="nav-icon messages">
                        📧
                    </div>
                    <div class="nav-title">消息管理</div>
                    <div class="nav-desc">查看消息发送日志和状态</div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6">
                <a href="/api" class="nav-card">
                    <div class="nav-icon success">
                        💻
                    </div>
                    <div class="nav-title">API接口</div>
                    <div class="nav-desc">查看和测试API接口</div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6">
                <a href="/setting" class="nav-card">
                    <div class="nav-icon pending">
                        ⚙️
                    </div>
                    <div class="nav-title">系统设置</div>
                    <div class="nav-desc">配置微信和短信服务参数</div>
                </a>
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="stat-card">
                    <h5 class="mb-3"><span class="me-2">💬</span>微信消息统计</h5>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h4 text-primary">{{ stats.wechat.total }}</div>
                            <small class="text-muted">总数</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-success">{{ stats.wechat.success }}</div>
                            <small class="text-muted">成功</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-info">{{ stats.wechat.success_rate }}%</div>
                            <small class="text-muted">成功率</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="stat-card">
                    <h5 class="mb-3"><span class="me-2">📱</span>短信消息统计</h5>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h4 text-primary">{{ stats.sms.total }}</div>
                            <small class="text-muted">总数</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-success">{{ stats.sms.success }}</div>
                            <small class="text-muted">成功</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-info">{{ stats.sms.success_rate }}%</div>
                            <small class="text-muted">成功率</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <p>&copy; 2025 中科生活消息平台. All rights reserved.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/common/bootstrap-5.3.0-dist/js/bootstrap.bundle.js"></script>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    stats: {
                        users: { total: 0, active: 0, today_new: 0, disabled: 0 },
                        messages: { total: 0, today: 0, success: 0, failed: 0, pending: 0, success_rate: 0 },
                        wechat: { total: 0, success: 0, success_rate: 0 },
                        sms: { total: 0, success: 0, success_rate: 0 }
                    },
                    // 消息管理相关数据
                    messageStats: {
                        total: 0,
                        success: 0,
                        failed: 0,
                        pending: 0,
                        wechat: { total: 0, success: 0, success_rate: 0 },
                        sms: { total: 0, success: 0, success_rate: 0 }
                    },
                    messages: [],
                    messageFilter: {
                        type: '',
                        status: '',
                        keyword: ''
                    },
                    pagination: {
                        current_page: 1,
                        last_page: 1,
                        per_page: 10,
                        total: 0
                    },
                    selectedMessage: null,
                    // 测试相关数据
                    wechatTest: {
                        touser: '',
                        template_id: '',
                        url: '',
                        data: '{\n  "character_string7": {"value": "ORD202501110001"},\n  "phrase3": {"value": "待审核"},\n  "time6": {"value": "2025-01-11 14:30:25"}\n}'
                    },
                    workTest: {
                        touser: '@all',
                        msgtype: 'text',
                        message: '这是一条来自系统的测试消息，用于验证企业微信配置是否正确。\n\n发送时间：' + new Date().toLocaleString()
                    },
                    wechatTestLoading: false,
                    workTestLoading: false,
                    testResults: []
                }
            },
            mounted() {
                this.loadStats();
            },
            methods: {
                // 加载统计数据
                async loadStats() {
                    try {
                        const response = await axios.get('/index/stats');
                        if (response.data && response.data.code === 0) {
                            this.stats = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                        // 保持默认值
                    }
                },

                // 显示即将推出提示
                showComingSoon(feature) {
                    alert(feature + ' 功能即将推出，敬请期待！');
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
