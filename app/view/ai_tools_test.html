<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI工具测试平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app" class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">🤖 AI工具测试平台</h2>
                
                <!-- 导航标签 -->
                <ul class="nav nav-tabs" id="aiToolsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">
                            📝 文本摘要
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sentiment-tab" data-bs-toggle="tab" data-bs-target="#sentiment" type="button" role="tab">
                            😊 情感分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="classify-tab" data-bs-toggle="tab" data-bs-target="#classify" type="button" role="tab">
                            🏷️ 内容分类
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="server-notice-tab" data-bs-toggle="tab" data-bs-target="#server-notice" type="button" role="tab">
                            🚨 服务器通知
                        </button>
                    </li>
                </ul>
                
                <!-- 标签内容 -->
                <div class="tab-content mt-3" id="aiToolsTabContent">
                    <!-- 文本摘要 -->
                    <div class="tab-pane fade show active" id="summary" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">📝 AI文本摘要生成</h5>
                            </div>
                            <div class="card-body">
                                <form @submit.prevent="generateSummary">
                                    <div class="mb-3">
                                        <label for="summaryText" class="form-label">原始文本</label>
                                        <textarea class="form-control" id="summaryText" rows="6" v-model="summaryForm.text" 
                                                  placeholder="请输入需要生成摘要的文本内容..." required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="maxLength" class="form-label">最大长度</label>
                                        <input type="number" class="form-control" id="maxLength" v-model="summaryForm.maxLength" 
                                               min="50" max="500" placeholder="200">
                                    </div>
                                    <button type="submit" class="btn btn-primary" :disabled="summaryLoading">
                                        <span v-if="summaryLoading" class="spinner-border spinner-border-sm me-2"></span>
                                        {{ summaryLoading ? '生成中...' : '生成摘要' }}
                                    </button>
                                </form>
                                
                                <div v-if="summaryResult" class="mt-4">
                                    <div class="alert" :class="summaryResult.success ? 'alert-success' : 'alert-danger'">
                                        <h6>{{ summaryResult.success ? '✅ 摘要生成成功' : '❌ 生成失败' }}</h6>
                                        <div v-if="summaryResult.success" class="mt-2">
                                            <strong>摘要内容：</strong>
                                            <p class="mt-2 mb-0">{{ summaryResult.data.summary }}</p>
                                        </div>
                                        <div v-else>
                                            <p class="mb-0">{{ summaryResult.message }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 情感分析 -->
                    <div class="tab-pane fade" id="sentiment" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">😊 AI情感分析</h5>
                            </div>
                            <div class="card-body">
                                <form @submit.prevent="analyzeSentiment">
                                    <div class="mb-3">
                                        <label for="sentimentText" class="form-label">待分析文本</label>
                                        <textarea class="form-control" id="sentimentText" rows="4" v-model="sentimentForm.text" 
                                                  placeholder="请输入需要分析情感的文本内容..." required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary" :disabled="sentimentLoading">
                                        <span v-if="sentimentLoading" class="spinner-border spinner-border-sm me-2"></span>
                                        {{ sentimentLoading ? '分析中...' : '分析情感' }}
                                    </button>
                                </form>
                                
                                <div v-if="sentimentResult" class="mt-4">
                                    <div class="alert" :class="sentimentResult.success ? 'alert-success' : 'alert-danger'">
                                        <h6>{{ sentimentResult.success ? '✅ 情感分析成功' : '❌ 分析失败' }}</h6>
                                        <div v-if="sentimentResult.success" class="mt-2">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>情感倾向：</strong>
                                                    <span class="badge" :class="getSentimentBadgeClass(sentimentResult.data.sentiment)">
                                                        {{ getSentimentText(sentimentResult.data.sentiment) }}
                                                    </span>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>置信度：</strong>
                                                    <span class="badge bg-info">{{ (sentimentResult.data.confidence * 100).toFixed(1) }}%</span>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <strong>具体情感：</strong>
                                                <div class="mt-1">
                                                    <span v-for="emotion in sentimentResult.data.emotions" :key="emotion" 
                                                          class="badge bg-secondary me-1">{{ emotion }}</span>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <strong>分析总结：</strong>
                                                <p class="mt-1 mb-0">{{ sentimentResult.data.summary }}</p>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <p class="mb-0">{{ sentimentResult.message }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 内容分类 -->
                    <div class="tab-pane fade" id="classify" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">🏷️ AI内容分类</h5>
                            </div>
                            <div class="card-body">
                                <form @submit.prevent="classifyContent">
                                    <div class="mb-3">
                                        <label for="classifyText" class="form-label">待分类文本</label>
                                        <textarea class="form-control" id="classifyText" rows="4" v-model="classifyForm.text" 
                                                  placeholder="请输入需要分类的文本内容..." required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="categories" class="form-label">可选分类（可选）</label>
                                        <input type="text" class="form-control" id="categories" v-model="classifyForm.categoriesText" 
                                               placeholder="例如：技术,商业,娱乐,体育（用逗号分隔）">
                                        <small class="form-text text-muted">留空则自动识别分类</small>
                                    </div>
                                    <button type="submit" class="btn btn-primary" :disabled="classifyLoading">
                                        <span v-if="classifyLoading" class="spinner-border spinner-border-sm me-2"></span>
                                        {{ classifyLoading ? '分类中...' : '内容分类' }}
                                    </button>
                                </form>
                                
                                <div v-if="classifyResult" class="mt-4">
                                    <div class="alert" :class="classifyResult.success ? 'alert-success' : 'alert-danger'">
                                        <h6>{{ classifyResult.success ? '✅ 内容分类成功' : '❌ 分类失败' }}</h6>
                                        <div v-if="classifyResult.success" class="mt-2">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>主要分类：</strong>
                                                    <span class="badge bg-primary">{{ classifyResult.data.category }}</span>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>子分类：</strong>
                                                    <span class="badge bg-secondary">{{ classifyResult.data.subcategory }}</span>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <strong>置信度：</strong>
                                                <span class="badge bg-info">{{ (classifyResult.data.confidence * 100).toFixed(1) }}%</span>
                                            </div>
                                            <div class="mt-2">
                                                <strong>标签：</strong>
                                                <div class="mt-1">
                                                    <span v-for="tag in classifyResult.data.tags" :key="tag" 
                                                          class="badge bg-success me-1">{{ tag }}</span>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <strong>分类理由：</strong>
                                                <p class="mt-1 mb-0">{{ classifyResult.data.reasoning }}</p>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <p class="mb-0">{{ classifyResult.message }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 服务器通知 -->
                    <div class="tab-pane fade" id="server-notice" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">🚨 服务器通知测试</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">
                                    服务器通知功能已集成AI优化，请访问 
                                    <a href="/server-notice-test" target="_blank">服务器通知测试页面</a> 
                                    进行测试。
                                </p>
                                <div class="d-grid">
                                    <a href="/server-notice-test" class="btn btn-outline-primary" target="_blank">
                                        🚀 打开服务器通知测试页面
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    // 文本摘要
                    summaryForm: {
                        text: '',
                        maxLength: 200
                    },
                    summaryLoading: false,
                    summaryResult: null,
                    
                    // 情感分析
                    sentimentForm: {
                        text: ''
                    },
                    sentimentLoading: false,
                    sentimentResult: null,
                    
                    // 内容分类
                    classifyForm: {
                        text: '',
                        categoriesText: ''
                    },
                    classifyLoading: false,
                    classifyResult: null
                }
            },
            methods: {
                async generateSummary() {
                    this.summaryLoading = true;
                    this.summaryResult = null;
                    
                    try {
                        const response = await axios.post('/api/aiGenerateSummary', {
                            text: this.summaryForm.text,
                            max_length: this.summaryForm.maxLength,
                            token:'zksh62576563'
                        });
                        
                        this.summaryResult = {
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        };
                    } catch (error) {
                        this.summaryResult = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                    } finally {
                        this.summaryLoading = false;
                    }
                },
                
                async analyzeSentiment() {
                    this.sentimentLoading = true;
                    this.sentimentResult = null;
                    
                    try {
                        const response = await axios.post('/api/aiAnalyzeSentiment', {
                            text: this.sentimentForm.text,
                            token:'zksh62576563'
                        });
                        
                        this.sentimentResult = {
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        };
                    } catch (error) {
                        this.sentimentResult = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                    } finally {
                        this.sentimentLoading = false;
                    }
                },
                
                async classifyContent() {
                    this.classifyLoading = true;
                    this.classifyResult = null;
                    
                    try {
                        const categories = this.classifyForm.categoriesText 
                            ? this.classifyForm.categoriesText.split(',').map(c => c.trim()).filter(c => c)
                            : [];
                            
                        const response = await axios.post('/api/aiClassifyContent', {
                            text: this.classifyForm.text,
                            categories: categories,
                            token:'zksh62576563'
                        });
                        
                        this.classifyResult = {
                            success: response.data.code === 0,
                            message: response.data.message,
                            data: response.data.data
                        };
                    } catch (error) {
                        this.classifyResult = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                    } finally {
                        this.classifyLoading = false;
                    }
                },
                
                getSentimentBadgeClass(sentiment) {
                    switch (sentiment) {
                        case 'positive': return 'bg-success';
                        case 'negative': return 'bg-danger';
                        case 'neutral': return 'bg-secondary';
                        default: return 'bg-secondary';
                    }
                },
                
                getSentimentText(sentiment) {
                    switch (sentiment) {
                        case 'positive': return '积极';
                        case 'negative': return '消极';
                        case 'neutral': return '中性';
                        default: return '未知';
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
