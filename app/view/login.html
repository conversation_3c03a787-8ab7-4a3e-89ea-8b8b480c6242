<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="/common/3.4.16"></script>
    <!-- Vue 3 CDN -->
    <script src="/common/vue.global.js"></script>
    
    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'wechat-green': '#1aad19',
                        'wechat-green-hover': '#16a317'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-soft': 'pulseSoft 2s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        pulseSoft: {
                            '0%, 100%': { transform: 'scale(1)' },
                            '50%': { transform: 'scale(1.05)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative">
    
    <!-- 背景图片层 -->
    <div class="absolute inset-0 z-0">
        <img 
            src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
            alt="Modern Office" 
            class="w-full h-full object-cover opacity-15"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-indigo-900/20"></div>
    </div>
    
    <!-- 主要内容区域 -->
    <div id="app" class="relative z-10 flex items-center justify-center h-full p-2 sm:p-4">
        
        <!-- 登录区域 -->
        <div class="w-full max-w-md flex items-center justify-center">
            
            <!-- 玻璃拟态登录卡片 -->
            <div class="w-full animate-slide-up">
                <div class="bg-white/20 backdrop-blur-xl border border-white/30 rounded-xl shadow-2xl p-4 sm:p-6">
                    
                    <!-- 登录标题 -->
                    <div class="text-center mb-6">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-wechat-green rounded-2xl flex items-center justify-center shadow-lg animate-pulse-soft">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.098c-.1.282.048.595.34.679a.68.68 0 0 0 .679-.34l1.098-.39a.59.59 0 0 1 .665.213c1.347 1.832 3.338 3.002 5.55 3.002 2.003 0 3.757-.85 4.965-2.213.402-.453.402-1.162 0-1.615-.453-.402-1.162-.402-1.615 0-.805.906-1.94 1.428-3.35 1.428-1.4 0-2.673-.734-3.393-1.940-.72-1.206-.72-2.674 0-3.88.734-1.4 1.993-2.134 3.393-2.134 1.4 0 2.659.734 3.393 2.134a.828.828 0 0 0 .734.453c.453 0 .828-.375.828-.828 0-4.054-3.891-7.342-8.691-7.342z"/>
                                    <circle cx="6" cy="9" r="1"/>
                                    <circle cx="12" cy="9" r="1"/>
                                    <path d="M16.5 13.5c2.8 0 5.5 1.4 5.5 3.5s-2.7 3.5-5.5 3.5c-.6 0-1.2-.1-1.7-.2l-1.4.5c-.1 0-.2 0-.3-.1s-.1-.2 0-.3l.5-1.1c-.8-.6-1.6-1.5-1.6-2.3 0-2.1 2.7-3.5 5.5-3.5z"/>
                                    <circle cx="15" cy="17" r="0.5"/>
                                    <circle cx="18" cy="17" r="0.5"/>
                                </svg>
                            </div>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800 mb-2">{{ loginTitle }}</h1>
                        <p class="text-gray-600 text-sm">{{ loginSubtitle }}</p>
                    </div>
                    
                    <!-- 条件渲染登录方式 -->
                    <div class="mb-6">
                        <!-- 非微信端：二维码扫码登录 -->
                        <div v-if="!isWeixin" class="bg-white/40 backdrop-blur-sm rounded-xl border border-white/50 overflow-hidden flex items-center justify-center">
                            <iframe 
                                src="https://auth.zkshlm.com/login_widget.html" 
                                frameborder="0" 
                                scrolling="no" 
                                width="100%" 
                                height="500"
                                class="w-full h-[500px] rounded-xl">
                            </iframe>
                        </div>
                        
                        <!-- 微信端：授权登录按钮 -->
                        <div v-if="isWeixin" class="space-y-4">
                            <button 
                                @click="wechatAuthLogin" 
                                :disabled="isLoading"
                                class="w-full bg-wechat-green hover:bg-wechat-green-hover disabled:bg-gray-400 text-white font-medium py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-0.5 flex items-center justify-center space-x-3 disabled:cursor-not-allowed">
                                <svg v-if="!isLoading" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.098c-.1.282.048.595.34.679a.68.68 0 0 0 .679-.34l1.098-.39a.59.59 0 0 1 .665.213c1.347 1.832 3.338 3.002 5.55 3.002 2.003 0 3.757-.85 4.965-2.213.402-.453.402-1.162 0-1.615-.453-.402-1.162-.402-1.615 0-.805.906-1.94 1.428-3.35 1.428-1.4 0-2.673-.734-3.393-1.940-.72-1.206-.72-2.674 0-3.88.734-1.4 1.993-2.134 3.393-2.134 1.4 0 2.659.734 3.393 2.134a.828.828 0 0 0 .734.453c.453 0 .828-.375.828-.828 0-4.054-3.891-7.342-8.691-7.342z"/>
                                    <circle cx="6" cy="9" r="1"/>
                                    <circle cx="12" cy="9" r="1"/>
                                    <path d="M16.5 13.5c2.8 0 5.5 1.4 5.5 3.5s-2.7 3.5-5.5 3.5c-.6 0-1.2-.1-1.7-.2l-1.4.5c-.1 0-.2 0-.3-.1s-.1-.2 0-.3l.5-1.1c-.8-.6-1.6-1.5-1.6-2.3 0-2.1 2.7-3.5 5.5-3.5z"/>
                                    <circle cx="15" cy="17" r="0.5"/>
                                    <circle cx="18" cy="17" r="0.5"/>
                                </svg>
                                <div v-if="isLoading" class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                <span>{{ isLoading ? '正在跳转...' : '微信一键登录' }}</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 状态提示区域 -->
                    <div class="text-center space-y-3">
                        <div class="bg-blue-50/50 border border-blue-200/50 rounded-lg p-3">
                            <p class="text-blue-700 text-sm leading-relaxed">
                                <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
                                {{ statusMessage }}
                            </p>
                        </div>
                        
                        <!-- 帮助信息 -->
                        <div class="flex items-center justify-center space-x-4 text-xs text-gray-400">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span>安全加密</span>
                            </span>
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span>快速便捷</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    


    <script type="text/javascript">
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isWeixin = ref(false);
                const isLoading = ref(false);

                console.log('Vue应用初始化，初始状态:', {
                    isWeixin: isWeixin.value,
                    isLoading: isLoading.value
                });
                
                // 存储keys
                const W_CODE_KEY = 'W_CODE';
                const AUTH_STATUS_KEY = 'AUTH_STATUS';
                const USER_ID_KEY = 'wx_user_id';
                const REDIRECT_URL_KEY = 'login_redirect_url';

                // 计算属性
                const loginTitle = computed(() => {
                    return isWeixin.value ? '微信登录' : '微信扫码登录';
                });

                const loginSubtitle = computed(() => {
                    return isWeixin.value ? '点击下方按钮完成授权' : '使用微信扫描二维码登录';
                });

                const statusMessage = computed(() => {
                    return isWeixin.value ? '点击按钮将跳转到微信授权页面' : '请使用微信扫描上方二维码完成登录';
                });

                // 工具函数
                const checkIsWeixin = () => {
                    return /micromessenger/i.test(navigator.userAgent);
                };

                const getQueryVariable = (variable) => {
                    const query = window.location.search.substring(1);
                    const vars = query.split('&');
                    const values = [];

                    console.log(`查找参数 ${variable}，原始查询字符串:`, query);

                    for (let i = 0; i < vars.length; i++) {
                        const pair = vars[i].split('=');
                        if (decodeURIComponent(pair[0]) === variable) {
                            values.push(decodeURIComponent(pair[1]));
                        }
                    }

                    console.log(`参数 ${variable} 的所有值:`, values);

                    // 如果有多个值，返回最后一个（通常是最新的）
                    const result = values.length > 0 ? values[values.length - 1] : null;
                    console.log(`参数 ${variable} 最终返回值:`, result);

                    return result;
                };

                const removeUrlParameter = (url, parameter) => {
                    const urlparts = url.split('?');
                    if (urlparts.length >= 2) {
                        const pars = urlparts[1].split(/[&;]/g);
                        // 移除所有匹配的参数（包括重复的）
                        const newpars = pars.filter(par => {
                            const paramName = par.split('=')[0];
                            return decodeURIComponent(paramName) !== parameter;
                        });
                        return urlparts[0] + (newpars.length > 0 ? '?' + newpars.join('&') : '');
                    }
                    return url;
                };

                // 微信H5登录 - "静默-升级"混合授权方案
                const wechatH5Login = async (code, spread_spid) => {
                    try {
                        console.log('开始"静默-升级"授权流程，code:', code);

                        // 调用新的混合授权接口
                        const response = await fetch('/api/wechatMpUserInfo', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                code: code,
                                state: spread_spid || 'default_state',
                                token: 'zksh62576563'
                            })
                        });

                        const result = await response.json();
                        console.log('授权接口响应:', result);

                        if (result.code === 0) {
                            // 静默授权成功，直接跳转
                            console.log('静默授权成功，直接跳转到主页面');
                            window.location.href = '/dashboard';
                            return result;
                        } else if (result.code === 1001) {
                            // 需要升级授权
                            console.log('需要升级授权，在当前页面处理');

                            // 显示升级授权提示
                            if (confirm('首次使用需要授权获取用户信息，是否继续？')) {
                                // 在当前页面进行认证授权
                                const redirectUri = encodeURIComponent(window.location.href);
                                const state = Math.random().toString(36).substring(2, 15);
                                const upgradeAuthUrl = `https://www.zkshlm.com/login3.php?param=${redirectUri}&state=${state}`;

                                console.log('跳转到认证授权页面:', upgradeAuthUrl);
                                window.location.href = upgradeAuthUrl;
                            }

                            return null;
                        } else {
                            // 其他错误
                            console.error('授权失败:', result.message);
                            alert(result.message || '授权失败，请重试');
                            return null;
                        }

                    } catch (error) {
                        console.error('Login error:', error);
                        alert('登录出错，请重试');
                        return null;
                    }
                };

                // 微信授权登录 - 静默授权优先
                const wechatAuthLogin = () => {
                    if (!isWeixin.value) {
                        alert('请在微信客户端中打开');
                        return;
                    }

                    isLoading.value = true;
                    console.log('开始微信"静默-升级"授权流程');

                    const state = Math.random().toString(36).substring(2, 15);
                    const redirectUri = encodeURIComponent(window.location.href);

                    // 构建静默授权URL (snsapi_base)
                    const authUrl = `https://www.zkshlm.com/login4.php?param=${redirectUri}&state=${state}`;

                    console.log('跳转到微信静默授权页面:', authUrl);
                    // 跳转到静默授权页面
                    window.location.href = authUrl;
                };

                // 处理微信回调
                const handleWechatCallback = async () => {
                    const code = getQueryVariable('code');
                    console.log('检查URL中的code参数:', code);

                    // 检查code是否有效（长度大于10的字符串，通常微信code较长）
                    if (code && code.length > 10) {
                        // 生成当前会话的唯一标识
                        const sessionId = Date.now().toString();
                        const codeKey = `${W_CODE_KEY}_${sessionId}`;

                        // 检查是否已经处理过这个code
                        const processedCodes = JSON.parse(localStorage.getItem('processed_codes') || '[]');

                        if (!processedCodes.includes(code)) {
                            // 标记这个code为已处理
                            processedCodes.push(code);
                            // 只保留最近的10个code记录
                            if (processedCodes.length > 10) {
                                processedCodes.shift();
                            }
                            localStorage.setItem('processed_codes', JSON.stringify(processedCodes));

                            const spread_spid = getQueryVariable('state') || localStorage.getItem('spread_spid');
                            console.log('处理微信回调，获取到code:', code, 'state:', spread_spid);

                            // 调用登录接口
                            await wechatH5Login(code, spread_spid);
                        } else {
                            console.log('code已处理过，跳过');
                        }
                    } else {
                        console.log('无效的code参数，跳过处理');
                    }
                };

                // 处理iframe消息
                const handleIframeMessage = async (event) => {
                    if (event.origin !== "https://auth.zkshlm.com") return;

                    const data = event.data;
                    if (data && data.type === 'wechat_login_code') {
                        console.log('收到授权码:', data.code);

                        try {
                            // 调用后端登录接口
                            console.log('二维码扫码登录，开始验证授权码:', data.code);

                            const response = await fetch('/api/wechatOpenUserInfo', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                credentials: 'include',
                                body: JSON.stringify({
                                    code: data.code,
                                    token: 'zksh62576563'
                                })
                            });

                            const result = await response.json();
                            console.log('登录接口响应:', result);

                            if (result.code === 0) {
                                // 二维码登录成功，直接跳转
                                console.log('二维码登录成功，直接跳转到主页面');
                                window.location.href = '/dashboard';
                            } else {
                                console.error('登录失败:', result.message);
                                alert(result.message || '登录失败，请重试');
                            }

                        } catch (error) {
                            console.error('登录请求失败:', error);
                            alert('登录出错，请重试');
                        }
                    }
                };

                // 初始化
                onMounted(async () => {
                    // 检测微信环境
                    isWeixin.value = checkIsWeixin();
                    console.log('初始化页面，微信环境:', isWeixin.value);
                    console.log('当前URL:', window.location.href);
                    
                    // 如果是微信环境且URL中有code参数，处理回调
                    const hasCodeParam = window.location.search.includes('code=');
                    console.log('URL中是否包含code参数:', hasCodeParam);
                    
                    if (isWeixin.value && hasCodeParam) {
                        console.log('检测到微信回调，开始处理...');
                        await handleWechatCallback();
                        
                        // 清理URL中的code和state参数
                        let cleanUrl = removeUrlParameter(window.location.href, 'code');
                        cleanUrl = removeUrlParameter(cleanUrl, 'state');
                        console.log('清理URL参数，原URL:', window.location.href, '新URL:', cleanUrl);
                        window.history.replaceState({}, document.title, cleanUrl);
                    } else {
                        console.log('普通页面访问，无需处理回调');
                    }
                    
                    // 监听iframe消息（非微信环境）
                    if (!isWeixin.value) {
                        console.log('非微信环境，开启iframe消息监听');
                        window.addEventListener("message", handleIframeMessage, false);
                    } else {
                        console.log('微信环境，不监听iframe消息');
                    }
                });



                return {
                    isWeixin,
                    isLoading,
                    loginTitle,
                    loginSubtitle,
                    statusMessage,
                    wechatAuthLogin
                };
            }
        }).mount('#app');
        
        // 确认Vue应用挂载成功
        console.log('Vue应用挂载完成，DOM Ready状态:', document.readyState);
    </script>
</body>
</html>