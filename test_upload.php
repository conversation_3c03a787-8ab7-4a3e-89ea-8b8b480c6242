<?php
/**
 * 文件上传功能测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/support/bootstrap.php';

use app\utils\S3FileUploader;

echo "=== 文件上传功能测试 ===\n\n";

// 测试配置获取
echo "1. 测试配置获取...\n";
$s3Config = [
    's3_endpoint' => getConfig('s3_endpoint'),
    's3_region' => getConfig('s3_region'),
    's3_access_key' => getConfig('s3_access_key'),
    's3_secret_key' => getConfig('s3_secret_key'),
    's3_bucket' => getConfig('s3_bucket'),
    's3_cdn_domain' => getConfig('s3_cdn_domain')
];

foreach ($s3Config as $key => $value) {
    echo "  {$key}: " . ($value ? '已配置' : '未配置') . "\n";
}

// 检查必需配置
$requiredConfigs = ['s3_endpoint', 's3_access_key', 's3_secret_key', 's3_bucket'];
$missingConfigs = [];
foreach ($requiredConfigs as $config) {
    if (empty($s3Config[$config])) {
        $missingConfigs[] = $config;
    }
}

if (!empty($missingConfigs)) {
    echo "\n❌ 缺少必需配置: " . implode(', ', $missingConfigs) . "\n";
    echo "请在系统设置中配置S3相关参数\n";
    exit(1);
}

echo "✅ 配置检查通过\n\n";

// 创建测试文件
echo "2. 创建测试文件...\n";
$testContent = "这是一个测试文件，用于验证文件上传功能。\n创建时间: " . date('Y-m-d H:i:s');
$testFileName = 'test_upload_' . date('YmdHis') . '.txt';
$testFilePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $testFileName;

if (file_put_contents($testFilePath, $testContent) === false) {
    echo "❌ 创建测试文件失败\n";
    exit(1);
}

echo "✅ 测试文件创建成功: {$testFilePath}\n";
echo "  文件大小: " . filesize($testFilePath) . " bytes\n";
echo "  文件哈希: " . hash_file('sha256', $testFilePath) . "\n\n";

// 测试文件上传
echo "3. 测试文件上传...\n";
$uploadResult = S3FileUploader::uploadFile($testFilePath, $testFileName, 'test_user_123');

if ($uploadResult['success']) {
    echo "✅ 文件上传成功\n";
    echo "  文件ID: " . $uploadResult['data']['id'] . "\n";
    echo "  文件URL: " . $uploadResult['data']['url'] . "\n";
    echo "  文件大小: " . $uploadResult['data']['size'] . " bytes\n";
    echo "  MIME类型: " . $uploadResult['data']['mime_type'] . "\n";
    echo "  是否秒传: " . ($uploadResult['data']['is_duplicate'] ? '是' : '否') . "\n";
    
    $fileId = $uploadResult['data']['id'];
} else {
    echo "❌ 文件上传失败: " . $uploadResult['message'] . "\n";
    unlink($testFilePath);
    exit(1);
}

echo "\n";

// 测试重复上传（秒传）
echo "4. 测试重复上传（秒传）...\n";
$duplicateResult = S3FileUploader::uploadFile($testFilePath, $testFileName, 'test_user_456');

if ($duplicateResult['success']) {
    echo "✅ 重复上传测试成功\n";
    echo "  是否秒传: " . ($duplicateResult['data']['is_duplicate'] ? '是' : '否') . "\n";
    echo "  文件ID: " . $duplicateResult['data']['id'] . "\n";
    
    if ($duplicateResult['data']['is_duplicate']) {
        echo "✅ 秒传功能正常工作\n";
    } else {
        echo "⚠️  秒传功能可能有问题，文件被重复上传了\n";
    }
} else {
    echo "❌ 重复上传测试失败: " . $duplicateResult['message'] . "\n";
}

echo "\n";

// 测试文件哈希检查
echo "5. 测试文件哈希检查...\n";
$fileHash = hash_file('sha256', $testFilePath);
$hashCheckResult = S3FileUploader::getFileByHash($fileHash);

if ($hashCheckResult) {
    echo "✅ 哈希检查成功\n";
    echo "  找到文件ID: " . $hashCheckResult['id'] . "\n";
    echo "  文件名: " . $hashCheckResult['file_name'] . "\n";
} else {
    echo "❌ 哈希检查失败，未找到文件\n";
}

echo "\n";

// 测试获取用户文件列表
echo "6. 测试获取用户文件列表...\n";
$userFiles = S3FileUploader::getUserFiles('test_user_123', 1, 10);

echo "✅ 用户文件列表获取成功\n";
echo "  总文件数: " . $userFiles['total'] . "\n";
echo "  当前页: " . $userFiles['page'] . "\n";
echo "  每页数量: " . $userFiles['limit'] . "\n";
echo "  总页数: " . $userFiles['pages'] . "\n";

if (!empty($userFiles['files'])) {
    echo "  文件列表:\n";
    foreach ($userFiles['files'] as $file) {
        echo "    - ID: {$file['id']}, 名称: {$file['file_name']}, 大小: {$file['size']} bytes\n";
    }
}

echo "\n";

// 测试文件删除
echo "7. 测试文件删除...\n";
if (isset($fileId)) {
    $deleteResult = S3FileUploader::deleteFile($fileId);
    
    if ($deleteResult['success']) {
        echo "✅ 文件删除成功\n";
    } else {
        echo "❌ 文件删除失败: " . $deleteResult['message'] . "\n";
    }
} else {
    echo "⚠️  跳过删除测试，因为没有有效的文件ID\n";
}

// 清理测试文件
echo "\n8. 清理测试文件...\n";
if (unlink($testFilePath)) {
    echo "✅ 本地测试文件清理成功\n";
} else {
    echo "⚠️  本地测试文件清理失败\n";
}

echo "\n=== 测试完成 ===\n";

// 输出测试总结
echo "\n测试总结:\n";
echo "- 配置检查: ✅\n";
echo "- 文件上传: " . ($uploadResult['success'] ? '✅' : '❌') . "\n";
echo "- 秒传功能: " . (isset($duplicateResult) && $duplicateResult['success'] && $duplicateResult['data']['is_duplicate'] ? '✅' : '⚠️') . "\n";
echo "- 哈希检查: " . ($hashCheckResult ? '✅' : '❌') . "\n";
echo "- 文件列表: ✅\n";
echo "- 文件删除: " . (isset($deleteResult) && $deleteResult['success'] ? '✅' : '⚠️') . "\n";

echo "\n如果所有测试都通过，说明文件上传功能正常工作！\n";
echo "你可以通过以下方式测试API接口:\n";
echo "1. 访问 http://your-domain.com/file-upload-test 使用Web界面测试\n";
echo "2. 使用cURL或Postman调用API接口\n";
echo "3. 查看API文档: docs/file_upload_api.md\n";
echo "\n注意: 请确保已安装AWS SDK for PHP:\n";
echo "composer install\n";
?>
