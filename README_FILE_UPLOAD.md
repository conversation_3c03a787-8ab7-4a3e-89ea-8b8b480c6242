# 文件上传系统

基于AWS SDK for PHP的S3兼容文件上传系统，支持文件哈希去重、秒传功能。

## 🚀 特性

- ✅ **S3兼容存储** - 支持AWS S3、MinIO、阿里云OSS等
- ✅ **文件去重** - 基于SHA256哈希值的智能去重
- ✅ **秒传功能** - 相同文件无需重复上传
- ✅ **批量上传** - 支持一次上传多个文件
- ✅ **安全认证** - API Token认证机制
- ✅ **文件管理** - 完整的CRUD操作
- ✅ **用户隔离** - 支持多用户文件管理
- ✅ **智能删除** - 引用计数，避免误删

## 📁 文件结构

```
├── app/
│   ├── controller/
│   │   └── FileController.php          # 文件上传控制器
│   ├── utils/
│   │   └── S3FileUploader.php          # S3文件上传工具类
│   └── view/
│       └── file_upload_test.html       # 文件上传测试页面
├── database/
│   └── wm_files.sql                    # 文件表结构
├── docs/
│   └── file_upload_api.md              # API文档
├── test_upload.php                     # PHP测试脚本
├── test_upload_curl.sh                 # Linux/Mac测试脚本
├── test_upload_curl.bat                # Windows测试脚本
└── README_FILE_UPLOAD.md               # 本文件
```

## 🛠️ 安装配置

### 1. 安装依赖

```bash
composer require aws/aws-sdk-php
```

### 2. 数据库配置

执行SQL文件创建文件表：

```sql
-- 已包含在 database/wm_files.sql 中
CREATE TABLE wm_files (
    id int PRIMARY KEY AUTO_INCREMENT,
    unionid varchar(255),
    file_hash varchar(64) NOT NULL,
    file_name varchar(255) NOT NULL,
    object_name varchar(500) NOT NULL,
    bucket_name varchar(100) NOT NULL,
    url varchar(1000) NOT NULL,
    size bigint NOT NULL,
    mime_type varchar(100),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_unionid (unionid),
    INDEX idx_file_hash (file_hash),
    INDEX idx_created_at (created_at)
);
```

### 3. S3配置

在系统设置中配置以下参数：

| 配置项 | 说明 | 示例 |
|--------|------|------|
| s3_endpoint | S3服务端点 | https://s3.amazonaws.com |
| s3_region | S3区域 | us-east-1 |
| s3_access_key | 访问密钥ID | AKIAIOSFODNN7EXAMPLE |
| s3_secret_key | 秘密访问密钥 | wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY |
| s3_bucket | 存储桶名称 | my-upload-bucket |
| s3_cdn_domain | CDN域名(可选) | https://cdn.example.com |

### 4. 路由配置

路由已自动配置在 `config/route.php` 中：

```php
// 文件上传API路由
Route::group('/api/file', function () {
    Route::post('/upload', [app\controller\FileController::class, 'upload']);
    Route::post('/delete', [app\controller\FileController::class, 'delete']);
    Route::get('/list', [app\controller\FileController::class, 'list']);
    Route::post('/check-hash', [app\controller\FileController::class, 'checkHash']);
    Route::get('/info', [app\controller\FileController::class, 'info']);
    Route::post('/batch-upload', [app\controller\FileController::class, 'batchUpload']);
});
```

## 🧪 测试

### 1. Web界面测试

访问测试页面：
```
http://your-domain.com/file-upload-test
```

### 2. 命令行测试

**Linux/Mac:**
```bash
chmod +x test_upload_curl.sh
./test_upload_curl.sh
```

**Windows:**
```cmd
test_upload_curl.bat
```

**PHP脚本:**
```bash
php test_upload.php
```

### 3. API测试示例

**上传文件:**
```bash
curl -X POST \
  'http://your-domain.com/api/file/upload?token=your_api_token' \
  -F 'file=@/path/to/file.jpg' \
  -F 'unionid=user123'
```

**检查文件是否存在:**
```bash
curl -X POST \
  'http://your-domain.com/api/file/check-hash?token=your_api_token' \
  -H 'Content-Type: application/json' \
  -d '{"file_hash":"sha256_hash_here"}'
```

## 📚 API接口

### 核心接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/file/upload` | POST | 单文件上传 |
| `/api/file/batch-upload` | POST | 批量文件上传 |
| `/api/file/check-hash` | POST | 检查文件是否存在(秒传) |
| `/api/file/list` | GET | 获取用户文件列表 |
| `/api/file/info` | GET | 获取文件详细信息 |
| `/api/file/delete` | POST | 删除文件 |

详细API文档请查看：[docs/file_upload_api.md](docs/file_upload_api.md)

## 🔧 核心功能

### 1. 文件哈希去重

```php
// 计算文件哈希
$fileHash = hash_file('sha256', $filePath);

// 检查是否已存在
$existingFile = Medoo::get('wm_files', '*', ['file_hash' => $fileHash]);
if ($existingFile) {
    // 秒传：直接返回已有文件信息
    return ['success' => true, 'data' => $existingFile, 'is_duplicate' => true];
}
```

### 2. AWS S3上传

```php
// 创建S3客户端
$s3Client = new S3Client([
    'version' => 'latest',
    'region' => $region,
    'endpoint' => $endpoint,
    'use_path_style_endpoint' => true,
    'credentials' => [
        'key' => $accessKey,
        'secret' => $secretKey,
    ]
]);

// 上传文件
$result = $s3Client->putObject([
    'Bucket' => $bucket,
    'Key' => $objectName,
    'SourceFile' => $filePath,
    'ContentType' => $mimeType,
    'ACL' => 'public-read'
]);
```

### 3. 智能删除

```php
// 检查引用计数
$sameHashCount = Medoo::count('wm_files', ['file_hash' => $fileHash]);

// 只有最后一个引用时才删除S3文件
if ($sameHashCount <= 1) {
    $s3Client->deleteObject([
        'Bucket' => $bucket,
        'Key' => $objectName
    ]);
}
```

## 🔒 安全特性

### 1. 文件类型限制

```php
$allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'text/plain', 'video/mp4', 'audio/mp3'
];
```

### 2. 文件大小限制

```php
$maxSize = 50 * 1024 * 1024; // 50MB
if ($file->getSize() > $maxSize) {
    return ['code' => 1, 'message' => '文件大小不能超过50MB'];
}
```

### 3. API Token认证

所有接口都需要有效的API Token：
```
GET /api/file/upload?token=your_api_token
```

## 🚀 性能优化

### 1. 配置缓存

```php
function getConfig(string $key, $default = null) {
    return \app\support\RedisCache::get($key, function() use ($key) {
        return \support\Medoo::get('wm_settings', 'value', ['key' => $key]);
    }, 300) ?: $default;
}
```

### 2. 数据库索引

```sql
INDEX idx_unionid (unionid),
INDEX idx_file_hash (file_hash),
INDEX idx_created_at (created_at)
```

### 3. CDN支持

配置CDN域名加速文件访问：
```php
if (!empty($cdnDomain)) {
    $url = $cdnDomain . '/' . $bucket . '/' . $objectName;
}
```

## 🐛 故障排除

### 1. 常见错误

**配置错误:**
```
S3配置不完整，请检查配置信息
```
解决：检查s3_endpoint、s3_access_key、s3_secret_key、s3_bucket配置

**权限错误:**
```
S3上传失败: Access Denied
```
解决：检查S3访问密钥权限，确保有PutObject权限

**网络错误:**
```
S3上传失败: Could not resolve host
```
解决：检查网络连接和S3端点URL

### 2. 调试模式

启用详细错误日志：
```php
try {
    // S3操作
} catch (AwsException $e) {
    error_log('AWS Error: ' . $e->getAwsErrorMessage());
    error_log('AWS Code: ' . $e->getAwsErrorCode());
}
```

## 📈 监控指标

### 1. 关键指标

- 上传成功率
- 秒传命中率
- 平均上传时间
- 存储空间使用量
- API调用频率

### 2. 日志记录

系统会自动记录：
- 文件上传日志
- 错误日志
- 性能指标
- 用户操作记录

## 🔄 版本更新

### v1.0.0
- ✅ 基础文件上传功能
- ✅ SHA256哈希去重
- ✅ AWS S3集成
- ✅ 批量上传支持
- ✅ 用户文件管理

### 后续计划
- 🔄 文件预览功能
- 🔄 图片缩略图生成
- 🔄 文件分享链接
- 🔄 上传进度回调
- 🔄 文件版本管理

## 📞 技术支持

如有问题，请查看：
1. [API文档](docs/file_upload_api.md)
2. 运行测试脚本进行诊断
3. 检查系统日志和错误信息
4. 验证S3配置和权限

---

**注意**: 请确保在生产环境中正确配置S3权限和网络安全策略。
