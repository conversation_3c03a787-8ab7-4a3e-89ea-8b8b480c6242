@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 文件上传API测试脚本 (Windows版本)
REM 使用cURL测试文件上传功能

REM 配置
set API_BASE_URL=http://10.0.0.247
set API_TOKEN=test_api_token_123456
set UNIONID=test_user_123

echo === 文件上传API测试 ===
echo API地址: %API_BASE_URL%
echo API Token: %API_TOKEN%
echo 用户ID: %UNIONID%
echo.

REM 创建测试文件
set TEST_FILE=test_upload_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set TEST_FILE=!TEST_FILE: =0!

echo 这是一个测试文件，用于验证文件上传功能。 > %TEST_FILE%
echo 创建时间: %date% %time% >> %TEST_FILE%

echo 1. 创建测试文件: %TEST_FILE%
for %%A in (%TEST_FILE%) do echo    文件大小: %%~zA bytes
echo.

REM 测试文件上传
echo 2. 测试文件上传...
curl -s -X POST ^
    "%API_BASE_URL%/api/file/upload?token=%API_TOKEN%" ^
    -F "file=@%TEST_FILE%" ^
    -F "unionid=%UNIONID%" > upload_response.json

echo 上传响应:
type upload_response.json
echo.

REM 尝试提取文件ID
for /f "tokens=*" %%i in ('powershell -command "try { $json = Get-Content upload_response.json | ConvertFrom-Json; if($json.code -eq 0) { $json.data.id } else { '' } } catch { '' }"') do set FILE_ID=%%i

if not "%FILE_ID%"=="" (
    echo 上传成功，文件ID: %FILE_ID%
    echo.

    REM 测试获取文件信息
    echo 3. 测试获取文件信息...
    curl -s -X GET ^
        "%API_BASE_URL%/api/file/info?token=%API_TOKEN%&file_id=%FILE_ID%" > file_info.json
    type file_info.json
    echo.

    REM 测试获取用户文件列表
    echo 4. 测试获取用户文件列表...
    curl -s -X GET ^
        "%API_BASE_URL%/api/file/list?token=%API_TOKEN%&unionid=%UNIONID%&page=1&limit=5" > file_list.json
    type file_list.json
    echo.

    REM 询问是否删除文件
    set /p DELETE_FILE="是否删除测试文件? (y/N): "
    if /i "%DELETE_FILE%"=="y" (
        echo 5. 测试删除文件...
        curl -s -X POST ^
            "%API_BASE_URL%/api/file/delete?token=%API_TOKEN%" ^
            -H "Content-Type: application/json" ^
            -d "{\"file_id\":%FILE_ID%}" > delete_response.json
        type delete_response.json
        echo.
    )
) else (
    echo 上传失败，跳过后续测试
)

REM 测试重复上传
echo 6. 测试重复上传（秒传功能）...
curl -s -X POST ^
    "%API_BASE_URL%/api/file/upload?token=%API_TOKEN%" ^
    -F "file=@%TEST_FILE%" ^
    -F "unionid=test_user_456" > duplicate_response.json

echo 重复上传响应:
type duplicate_response.json
echo.

REM 检查是否为秒传
for /f "tokens=*" %%i in ('powershell -command "try { $json = Get-Content duplicate_response.json | ConvertFrom-Json; if($json.code -eq 0 -and $json.data.is_duplicate) { 'true' } else { 'false' } } catch { 'false' }"') do set IS_DUPLICATE=%%i

if "%IS_DUPLICATE%"=="true" (
    echo ✅ 秒传功能正常工作
) else (
    echo ⚠️  秒传功能可能有问题
)
echo.

REM 测试批量上传
echo 7. 测试批量上传...

REM 创建第二个测试文件
set TEST_FILE2=test_upload_batch_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set TEST_FILE2=!TEST_FILE2: =0!

echo 这是第二个测试文件，用于测试批量上传功能。 > %TEST_FILE2%
echo 创建时间: %date% %time% >> %TEST_FILE2%

curl -s -X POST ^
    "%API_BASE_URL%/api/file/batch-upload?token=%API_TOKEN%" ^
    -F "files[]=@%TEST_FILE%" ^
    -F "files[]=@%TEST_FILE2%" ^
    -F "unionid=%UNIONID%" > batch_response.json

echo 批量上传响应:
type batch_response.json
echo.

REM 清理测试文件
echo 8. 清理本地测试文件...
del /q %TEST_FILE% %TEST_FILE2% 2>nul
del /q *.json 2>nul
echo ✅ 本地测试文件已清理
echo.

echo === 测试完成 ===
echo.
echo 测试说明:
echo - 如果看到 'code': 0，表示操作成功
echo - 如果看到 'code': 1，表示操作失败，请检查错误信息
echo - 秒传功能会在相同文件再次上传时自动识别并返回已有文件信息
echo - 批量上传支持一次上传多个文件
echo.
echo 更多信息请查看API文档: docs/file_upload_api.md

pause
