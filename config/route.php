<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use app\middleware\AdminAuthMiddleware;
use Webman\Route;

// 需要鉴权的页面
Route::group('', function () {
    // 页面路由配置
    Route::get('/', function() {
        return view('index');
    });

    // 页面路由配置
    Route::get('/dashboard', function() {
        return view('index');
    });

    Route::get('/setting', function() {
        return view('setting');
    });

    Route::get('/api', function() {
        return view('api');
    });

    Route::get('/server-notice-test', function() {
        return view('server_notice_test');
    });

    Route::get('/ai-tools-test', function() {
        return view('ai_tools_test');
    });

    Route::get('/message', function() {
        return view('message');
    });

    Route::get('/file-upload-test', function() {
        return view('file_upload_test');
    });
})->middleware([AdminAuthMiddleware::class]);

Route::get('/login', function() {
    return view('login');
});

// 文件上传API路由
Route::group('/api/file', function () {
    Route::post('/upload', [app\controller\FileController::class, 'upload']);
    Route::post('/delete', [app\controller\FileController::class, 'delete']);
    Route::get('/list', [app\controller\FileController::class, 'list']);
    Route::post('/check-hash', [app\controller\FileController::class, 'checkHash']);
    Route::get('/info', [app\controller\FileController::class, 'info']);
    Route::post('/batch-upload', [app\controller\FileController::class, 'batchUpload']);
});

Route::fallback(function(){
    return redirect('/404.html');
});






