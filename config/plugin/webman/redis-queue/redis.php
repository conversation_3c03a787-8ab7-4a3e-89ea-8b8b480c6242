<?php
return [
    'default' => [
        'host' => 'redis://'.config('redis.default.host').':'.config('redis.default.port'),
        'options' => [
            'auth' => config('redis.default.password'),        // 密码，字符串类型，可选参数
            'db' => config('redis.default.database'),          // 数据库
            'prefix' => '',
            'max_attempts'  => 5,
            'retry_seconds' => 5,
        ],
        // Connection pool, supports only Swoole or Swow drivers.
        'pool' => [
            'max_connections' => 5,
            'min_connections' => 1,
            'wait_timeout' => 3,
            'idle_timeout' => 60,
            'heartbeat_interval' => 50,
        ]
    ],
];
