# .github/workflows/docker-publish.yml

# 工作流名称
name: Build, Push, and Notify

# 触发条件：当代码被推送到 main 分支时触发
on:
  push:
    branches:
      - main

jobs:
  build-push-and-notify:
    # 指定运行环境
    runs-on: ubuntu-latest

    steps:
      # 步骤 1: 检出代码
      - name: Checkout repository
        uses: actions/checkout@v4

      # 步骤 2: 登录到阿里云容器镜像服务(ACR)
      - name: Log in to Aliyun Container Registry
        uses: docker/login-action@v3
        with:
          registry: registry.cn-beijing.aliyuncs.com
          username: ${{ secrets.ALIYUN_USERNAME }}
          password: ${{ secrets.ALIYUN_PASSWORD }}

      # 步骤 3: 从 Dockerfile 构建并推送镜像
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            registry.cn-beijing.aliyuncs.com/zksh/zksh_common_api:latest
            registry.cn-beijing.aliyuncs.com/zksh/zksh_common_api:${{ github.sha }}

      # --- 新增步骤 ---
      # 步骤 4: 发送更新通知 (Webhook)
      - name: Send update notification
        run: |
          curl -f -H "Authorization: Bearer ${{ secrets.DEPLOY_TOKEN }}" http://120.46.66.160:8383/v1/update