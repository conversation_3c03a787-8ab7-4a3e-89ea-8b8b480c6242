#!/bin/bash

# 文件上传API测试脚本
# 使用cURL测试文件上传功能

# 配置
API_BASE_URL="http://10.0.0.247"
API_TOKEN="test_api_token_123456"
UNIONID="test_user_123"

echo "=== 文件上传API测试 ==="
echo "API地址: $API_BASE_URL"
echo "API Token: $API_TOKEN"
echo "用户ID: $UNIONID"
echo ""

# 创建测试文件
TEST_FILE="test_upload_$(date +%Y%m%d_%H%M%S).txt"
echo "这是一个测试文件，用于验证文件上传功能。" > $TEST_FILE
echo "创建时间: $(date)" >> $TEST_FILE
echo "文件大小: $(wc -c < $TEST_FILE) bytes" >> $TEST_FILE

echo "1. 创建测试文件: $TEST_FILE"
echo "   文件大小: $(wc -c < $TEST_FILE) bytes"
echo ""

# 计算文件哈希值
if command -v sha256sum &> /dev/null; then
    FILE_HASH=$(sha256sum $TEST_FILE | cut -d' ' -f1)
elif command -v shasum &> /dev/null; then
    FILE_HASH=$(shasum -a 256 $TEST_FILE | cut -d' ' -f1)
else
    echo "警告: 无法计算文件哈希值，跳过哈希检查测试"
    FILE_HASH=""
fi

if [ ! -z "$FILE_HASH" ]; then
    echo "   文件哈希: $FILE_HASH"
    echo ""

    # 测试哈希检查（秒传检查）
    echo "2. 测试哈希检查（秒传检查）..."
    curl -s -X POST \
        "$API_BASE_URL/api/file/check-hash?token=$API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"file_hash\":\"$FILE_HASH\"}" \
        | python3 -m json.tool 2>/dev/null || echo "响应格式错误"
    echo ""
fi

# 测试文件上传
echo "3. 测试文件上传..."
UPLOAD_RESPONSE=$(curl -s -X POST \
    "$API_BASE_URL/api/file/upload?token=$API_TOKEN" \
    -F "file=@$TEST_FILE" \
    -F "unionid=$UNIONID")

echo "上传响应:"
echo "$UPLOAD_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$UPLOAD_RESPONSE"
echo ""

# 提取文件ID（如果上传成功）
FILE_ID=$(echo "$UPLOAD_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('code') == 0:
        print(data['data']['id'])
    else:
        print('')
except:
    print('')
" 2>/dev/null)

if [ ! -z "$FILE_ID" ]; then
    echo "上传成功，文件ID: $FILE_ID"
    echo ""

    # 测试获取文件信息
    echo "4. 测试获取文件信息..."
    curl -s -X GET \
        "$API_BASE_URL/api/file/info?token=$API_TOKEN&file_id=$FILE_ID" \
        | python3 -m json.tool 2>/dev/null || echo "响应格式错误"
    echo ""

    # 测试获取用户文件列表
    echo "5. 测试获取用户文件列表..."
    curl -s -X GET \
        "$API_BASE_URL/api/file/list?token=$API_TOKEN&unionid=$UNIONID&page=1&limit=5" \
        | python3 -m json.tool 2>/dev/null || echo "响应格式错误"
    echo ""

    # 询问是否删除文件
    read -p "是否删除测试文件? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "6. 测试删除文件..."
        curl -s -X POST \
            "$API_BASE_URL/api/file/delete?token=$API_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{\"file_id\":$FILE_ID}" \
            | python3 -m json.tool 2>/dev/null || echo "响应格式错误"
        echo ""
    fi
else
    echo "上传失败，跳过后续测试"
fi

# 测试重复上传（如果哈希值可用）
if [ ! -z "$FILE_HASH" ]; then
    echo "7. 测试重复上传（秒传功能）..."
    DUPLICATE_RESPONSE=$(curl -s -X POST \
        "$API_BASE_URL/api/file/upload?token=$API_TOKEN" \
        -F "file=@$TEST_FILE" \
        -F "unionid=test_user_456")

    echo "重复上传响应:"
    echo "$DUPLICATE_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$DUPLICATE_RESPONSE"
    echo ""

    # 检查是否为秒传
    IS_DUPLICATE=$(echo "$DUPLICATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('code') == 0 and data.get('data', {}).get('is_duplicate'):
        print('true')
    else:
        print('false')
except:
    print('false')
" 2>/dev/null)

    if [ "$IS_DUPLICATE" = "true" ]; then
        echo "✅ 秒传功能正常工作"
    else
        echo "⚠️  秒传功能可能有问题"
    fi
    echo ""
fi

# 测试批量上传
echo "8. 测试批量上传..."

# 创建第二个测试文件
TEST_FILE2="test_upload_batch_$(date +%Y%m%d_%H%M%S).txt"
echo "这是第二个测试文件，用于测试批量上传功能。" > $TEST_FILE2
echo "创建时间: $(date)" >> $TEST_FILE2

BATCH_RESPONSE=$(curl -s -X POST \
    "$API_BASE_URL/api/file/batch-upload?token=$API_TOKEN" \
    -F "files[]=@$TEST_FILE" \
    -F "files[]=@$TEST_FILE2" \
    -F "unionid=$UNIONID")

echo "批量上传响应:"
echo "$BATCH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$BATCH_RESPONSE"
echo ""

# 清理测试文件
echo "9. 清理本地测试文件..."
rm -f $TEST_FILE $TEST_FILE2
echo "✅ 本地测试文件已清理"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "测试说明:"
echo "- 如果看到 'code': 0，表示操作成功"
echo "- 如果看到 'code': 1，表示操作失败，请检查错误信息"
echo "- 秒传功能会在相同文件再次上传时自动识别并返回已有文件信息"
echo "- 批量上传支持一次上传多个文件"
echo ""
echo "更多信息请查看API文档: docs/file_upload_api.md"
