{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.1", "workerman/webman-framework": "^2.1", "monolog/monolog": "^2.0", "webman/medoo": "^2.1", "vlucas/phpdotenv": "^5.6", "webman/redis-queue": "^2.1", "w7corp/easywechat": "^6.17", "webman/redis": "^2.1", "illuminate/events": "^10.48", "webman/think-orm": "^2.1", "tinywan/exception-handler": "^1.6", "siam-yon/sms": "^1.1", "ext-curl": "*", "ext-fileinfo": "*", "aws/aws-sdk-php": "^3.337"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true}