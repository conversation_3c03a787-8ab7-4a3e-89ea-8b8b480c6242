<?php
declare(strict_types=1);

namespace support;

use Tinywan\ExceptionHandler\Handler;
use Webman\Http\Response;
use Webman\RedisQueue\Redis;
use support\Log;

/**
 * 自定义异常处理器
 * 基于webman-exception最佳实践
 */
class ErrorHandler extends Handler
{
    /**
     * 不需要通知的HTTP状态码
     */
    private const IGNORE_NOTIFY_CODES = [400, 401, 402, 403, 404, 410, 422, 429];



    /**
     * 触发异常通知事件
     * @param \Throwable $e
     */
    protected function triggerNotifyEvent(\Throwable $e): void
    {
        try {
            // 记录详细错误日志
            $this->logError($e);
            $isServerError = (int)$this->statusCode >= 500;
            if ($isServerError && $this->shouldNotify($e)) {
                $this->sendNotification($e);
            }
        } catch (\Throwable $notifyException) {
            // 通知发送失败时记录日志，避免影响主流程
            Log::error('异常通知发送失败', [
                'original_error' => $e->getMessage(),
                'notify_error' => $notifyException->getMessage()
            ]);
        }

        // 调用父类方法，保持扩展性
        parent::triggerNotifyEvent($e);
    }

    /**
     * 记录错误日志
     * @param \Throwable $e
     */
    private function logError(\Throwable $e): void
    {
        $context = [
            'exception_class' => get_class($e),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'status_code' => $this->statusCode,
            'error_message' => $this->errorMessage,
        ];

        // 添加请求相关信息
        if (!empty($this->responseData)) {
            $context = array_merge($context, [
                'request_url' => $this->responseData['request_url'] ?? '',
                'client_ip' => $this->responseData['client_ip'] ?? '',
                'request_method' => $this->responseData['request_method'] ?? '',
                'user_agent' => $this->responseData['user_agent'] ?? '',
            ]);
        }

        Log::error("系统异常: {$this->errorMessage}", $context);
    }

    /**
     * 判断是否需要发送通知
     * @param \Throwable $e
     * @return bool
     */
    private function shouldNotify(\Throwable $e): bool
    {
        // 忽略特定的HTTP状态码
        if (in_array((int)$this->statusCode, self::IGNORE_NOTIFY_CODES)) {
            return false;
        }

        // 忽略特定的异常代码
        if ($e->getCode() === -1) {
            return false;
        }

        // 只通知服务器错误（5xx）和严重的客户端错误
        return (int)$this->statusCode >= 500 || (int)$this->statusCode === 405;
    }

    /**
     * 发送异常通知
     * @param \Throwable $e
     */
    private function sendNotification(\Throwable $e): void
    {
        $errorType = $this->getErrorType($e);
        $severity = $this->getErrorSeverity((int)$this->statusCode);
        $content = $this->buildErrorContent($e, $errorType, $severity);

        $notificationData = [
            'title' => "🚨 {$errorType} - {$severity}",
            'content' => $content,
            'level' => $severity,
            'timestamp' => time(),
            'environment' => config('app.env', 'production')
        ];

        // 发送到队列
        Redis::send('send_server', $notificationData);
    }

    /**
     * 获取错误类型
     * @param \Throwable $e
     * @return string
     */
    private function getErrorType(\Throwable $e): string
    {
        $className = get_class($e);

        switch (true) {
            case strpos($className, 'Database') !== false:
            case strpos($className, 'PDO') !== false:
                return '数据库错误';
            case strpos($className, 'Redis') !== false:
                return 'Redis错误';
            case strpos($className, 'Http') !== false:
            case strpos($className, 'Curl') !== false:
                return '网络请求错误';
            case strpos($className, 'File') !== false:
            case strpos($className, 'Directory') !== false:
                return '文件系统错误';
            case strpos($className, 'Parse') !== false:
            case strpos($className, 'Syntax') !== false:
                return '语法错误';
            case strpos($className, 'Fatal') !== false:
                return '致命错误';
            case strpos($className, 'Memory') !== false:
                return '内存错误';
            default:
                return '系统异常';
        }
    }

    /**
     * 获取错误严重程度
     * @param int $statusCode
     * @return string
     */
    private function getErrorSeverity(int $statusCode): string
    {
        switch (true) {
            case $statusCode >= 500:
                return '严重';
            case $statusCode >= 400:
                return '警告';
            default:
                return '提示';
        }
    }

    /**
     * 构建通知内容
     * 符合最佳实践的错误通知格式
     * @param \Throwable $e
     * @param string $errorType
     * @param string $severity
     * @return string
     */
    private function buildErrorContent(\Throwable $e, string $errorType, string $severity): string
    {
        $env = getenv('APP_NAME') ?: 'Unknown';
        $timestamp = date('Y-m-d H:i:s');
        $url = $this->responseData['request_url'] ?? 'Unknown';

        return "🚨 {$errorType} - {$severity}\n\n" .
               "📋 错误: {$this->errorMessage}\n" .
               "📁 文件: " . basename($e->getFile()) . ":{$e->getLine()}\n" .
               "🌐 请求: {$url}\n" .
               "🔧 环境: {$env}\n" .
               "⏰ 时间: {$timestamp}";
    }



    /**
     * 构建响应
     * 符合webman-exception最佳实践
     * @return Response
     */
    protected function buildResponse(): Response
    {   
        $statusCode = (int)$this->statusCode;
        // 非400返回
        if ($statusCode >= 500) {
            // 构造自己项目下的响应
            return new Response($statusCode, ['Content-Type' => 'application/json'], json_encode([
                'code' => $this->statusCode, // 使用 statusCode 作为 code 返回
                'message' => config('app.debug') ? $this->errorMessage : '网络异常，请稍后再试',
                'data' => config('app.debug') ? $this->responseData : [],
            ], JSON_UNESCAPED_UNICODE));
        }
        return new Response($statusCode, ['Content-Type' => 'application/json'], json_encode([
            'code' => $this->statusCode, // 使用 statusCode 作为 code 返回
            'message' => $this->errorMessage,
            'data' => config('app.debug') ? $this->responseData : [],
        ], JSON_UNESCAPED_UNICODE));
    }
}