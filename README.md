# 中科生活消息平台 - 项目开发文档

文档创建时间: 2025 年 7 月 8 日

## 1. 项目概述

本项目旨在创建一个通用、高效、可扩展的消息通知平台。平台的核心任务是作为中心枢纽，接收来自各类业务系统的指令，并通过微信模板消息和短信等多种渠道，准确地将信息触达终端用户。同时，平台需提供一个功能完善的管理后台，以便对用户、消息及系统配置进行统一管理和监控。

## 2. 技术选型

### 后端

- **后端框架**: Webman (基于 Workerman 的高性能常驻内存框架)
- **数据库 ORM**: Medoo (轻量级的数据库操作库)
- **数据缓存**: Redis (用于缓存高频访问的数据，如微信 AccessToken)
- **HTTP 客户端**: Guzzle (用于与外部 API 进行通信)

### 前端

- **基础结构**: HTML5，由 Webman 后端渲染。
- **JS 框架**: Vue 3 (通过 CDN 引入)
- **CSS 框架**:

    - Bootstrap 5 (通过 CDN 引入)
    - Tailwind CSS (通过 Play CDN 引入)
- **HTTP 客户端**: Axios (通过 CDN 引入)

## 3. 核心功能规划

### 3.1 微信用户授权与身份统一流程

1. **接口接收**: 前端或第三方应用调用平台的统一授权接口，例如 `POST /api/auth/wechat`。请求体中必须包含从微信获取的 `code`
   和标识当前应用的 `app_id`。
2. **Code 换取身份**:

- `ApiController` 接收到请求，验证 `code` 和 `app_id` 的有效性。
- 调用 `WeChatService`，将 `code` 传递给它。
- `WeChatService` 使用封装的 HTTP 客户端请求微信的 `jscode2session` 或相关接口，换取 `openid` 和 `unionid`。
- 如果微信接口返回错误（如 `code` 失效），则向调用方返回错误信息。

3. **主用户查询与创建**:

- 获取到 `unionid` 后，使用 `WechatUser` 模型查询数据库主用户表：`WechatUser::where('unionid', $unionid)->find()`。
- 若用户不存在:

    - 调用微信 API 获取用户的昵称、头像等公开信息。
    - 创建一个新的 `WechatUser` 模型实例，填充 `unionid` 及用户信息，并保存到数据库。
    - 获取新创建记录的自增 `id` 作为 `user_id`。
- 若用户已存在: 直接获取其 `id` 作为 `user_id`。

4. **身份标识关联**:

- 使用 `WechatIdentity` 模型，根据 `app_id` 和 `openid` 查询身份表：
  `WechatIdentity::where(['app_id' => $app_id, 'openid' => $openid])->find()`。
- 若身份记录不存在:

    - 创建一个新的 `WechatIdentity` 模型实例。
    - 填充 `user_id`（来自上一步）、`app_id` 和 `openid`。
    - 保存到数据库，完成用户主记录与当前应用身份的绑定。
- 若身份记录已存在: 无需操作。

5. **信息返回**:

- 将查询或创建好的主用户信息（`wm_wechat_users` 表中的记录）作为业务数据。
- `ApiController` 将这些数据封装成统一的 JSON 格式（如包含用户 ID、昵称、头像、自定义 Token 等），返回给接口调用方。

### 3.2 异步消息发送流程

1. **接口接收**: 业务系统调用消息发送接口，例如 `POST /api/notice/send`。请求体包含 `user_id` (平台统一用户 ID), `type` (
   `wechat`/`sms`), `template_id` 和 `data` (模板内容)。
2. **请求预处理与入队**:

- `NoticeController` 接收请求并进行严格的参数验证。
- 立即创建日志: 在 `wm_message_logs` 表中创建一条新的日志记录，`status` 初始设置为 0 (待发送)。
- 投递到消息队列: 将包含日志 ID 和所有请求参数的数据，作为一个任务（Job）推送到 Redis 消息队列中。
- 快速响应: 任务入队成功后，立即向接口调用方返回成功响应，告知“消息已接收，正在处理中”。

3. **后台队列消费**:

- 一个或多个独立的 Webman 消费者进程（Process）持续监听 Redis 队列。
- 当队列中有新任务时，消费者进程获取任务数据。

4. **消息调度与发送**:

- 消费者调用 `NoticeService`，并将任务数据传递给它。
- `NoticeService` 根据任务中的 `type` 字段进行调度：

    - 若 `type` 为 `wechat`:

        - 调用 `WeChatService`。
        - `WeChatService` 需要根据 `user_id` 和 `template_id` 中的 `app_id`（或通过配置指定）来获取接收者在该应用下的
          `openid`。
        - 调用微信模板消息 API 发送消息。
    - 若 `type` 为 `sms`:

        - 调用 `SmsService`。
        - `SmsService` 从用户表中获取手机号（需提前设计手机号字段或从第三方获取）。
        - 调用短信服务商的 SDK 发送短信。

5. **结果处理与日志更新**:

- `WeChatService` 或 `SmsService` 在调用第三方 API 后，会得到一个返回结果。
- 根据返回结果，判断发送是成功还是失败。
- 使用任务数据中的日志 ID，更新 `wm_message_logs` 表中对应记录的 `status` (改为 1 成功或 2 失败)，并将服务商返回的完整
  response 报文存入相应字段。

## 4. 前端开发说明

### 4.1 前端技术选型

- **基础结构**: HTML5，由 Webman 后端渲染。
- **JS 框架**: Vue 3 (通过 CDN 引入)
- **CSS 框架**:

    - Bootstrap 5 (通过 CDN 引入)
    - Tailwind CSS (通过 Play CDN 引入)
- **HTTP 客户端**: Axios (通过 CDN 引入)

### 4.2 开发模式说明

- **后端驱动视图**: Webman 作为后端服务器，负责渲染每个页面的基础 HTML 骨架和布局。
- **混合 CSS 策略**: 开发中将结合使用 Bootstrap 和 Tailwind CSS。

    - **优先使用 Bootstrap 组件**: 当需要快速实现标准组件（如模态框、警告框、带验证样式的表单）时，直接使用 Bootstrap 预设的
      class 和 JavaScript 插件。
    - **使用 Tailwind 进行微调和定制**: 使用 Tailwind 的功能类来调整间距、颜色、圆角、阴影、Hover 效果等，实现更精细的视觉定制，或覆盖
      Bootstrap 的默认样式。
- **Vue 渐进式增强**: Vue 3 在每个独立的 HTML 页面上挂载，为静态页面提供动态交互能力，而非构建单页应用（SPA）。
- **API 数据分离**: 页面中的所有动态数据均通过 Axios 异步请求后端的 API 接口来获取。后端控制器需提供两类接口：一类用于渲染
  HTML 页面，另一类（通常以 `/api/` 开头）用于提供 JSON 数据。

### 4.3 基础布局与资源引入逻辑

- **主布局文件**: 所有后台页面将共享一个主布局文件 `app/view/layout.html`。该文件负责定义页面整体结构，并按照正确的顺序引入所有
  CDN 资源。
- **资源引入顺序**:

    - 在 `<head>` 标签中，首先引入 Bootstrap CSS 的 CDN 链接。
    - 接着引入 Tailwind CSS 的 Play CDN 链接。
    - 在 `<body>` 标签的末尾，首先引入 Vue 3 的 CDN 链接。
    - 然后引入 Axios 的 CDN 链接。
    - 最后引入 Bootstrap JS Bundle (包含 Popper.js) 的 CDN 链接，以确保 Bootstrap 的动态组件可以正常工作。
- **内容占位**: 布局文件中会包含一个 Webman 视图引擎的占位符（如 `{{template_content}}`），用于将各个页面的具体内容动态插入到主布局中。

### 4.4 页面开发业务逻辑（以用户管理为例）

- **HTML 结构逻辑**:

    - 在具体的页面文件（如 `app/view/admin/user/index.html`）中，首先声明继承自主布局文件。
    - 页面需要一个根元素（如 `<div id="userApp">`），作为当前页面 Vue 应用的挂载点。
    - 在此根元素内部，开发者可以使用 Bootstrap 的 class 构建基础布局和组件。
    - 同时，可以使用 Tailwind 的 class 对这些元素进行样式增强。
    - 使用 Vue 的模板指令（如 `v-for`, `v-model`, `@click`）将这些静态结构与 Vue 实例的数据和方法进行绑定。
- **JavaScript (Vue) 业务逻辑**:

    - 在页面的 `<script>` 标签内，初始化一个 Vue 3 应用实例。
    - **状态管理**: 使用 Vue 的响应式 API（如 `ref`）来定义页面所需的所有状态。
    - **初始化数据**: 在 `onMounted` 生命周期钩子中，调用一个方法（如 `fetchUsers`）。该方法会使用 Axios 向后端的
      `/api/admin/users` 接口发起 GET 请求，获取第一页的用户数据，并更新到 `users` 状态中。
    - **事件处理**: 定义一系列方法来响应用户的操作。

        - `searchUsers()`: 当用户在搜索框中输入并回车时触发，该方法会调用 `fetchUsers` 并附带上搜索关键词参数。
        - `openEditModal(user)`: 当点击某用户的“编辑”按钮时触发。该方法会将传入的 `user` 对象深拷贝到 `editingUser`
          状态中，然后使用 Bootstrap 的 JavaScript API 来手动显示编辑模态框。
        - `submitUserForm()`: 在模态框中点击“保存”时触发。该方法会使用 Axios 向后端对应的 API 接口（如
          `POST /api/admin/users/update`）发送 POST 请求，请求体中包含 `editingUser` 的数据。请求成功后，关闭模态框并刷新用户列表。
    - **应用挂载**: 最后，将创建的 Vue 实例挂载到预设的 HTML 根元素上（`.mount('#userApp')`）。

## 5. 目录结构设计

复制

```
.
├── app
│   ├── controller                # 控制器层：处理路由、参数校验、调用服务
│   │   ├── ApiController.php      # 负责所有对外的、供机器调用的 API
│   │   └── admin                # 负责所有后台管理页面的控制器
│   │       ├── IndexController.php     # 仪表盘与后台首页
│   │       ├── UserController.php      # 用户管理
│   │       ├── MessageController.php   # 消息日志管理
│   │       └── SettingController.php   # 系统设置管理
│   │
│   ├── service                   # 服务层：封装所有核心业务逻辑
│   │   ├── WeChatService.php    # 封装微信 API 相关的所有操作
│   │   ├── SmsService.php       # 封装短信服务商 SDK 的所有操作
│   │   ├── NoticeService.php    # 统一的消息调度服务
│   │   └── SettingService.php   # 封装后台设置的读取与存储逻辑
│   │
│   └── view                      # 视图层：存放所有 HTML 模板文件
│       ├── layout.html          # 后台通用布局文件（头部、侧边栏、底部）
│       └── admin
│           ├── index
│           │   └── index.html
│           ├── user
│           │   └── index.html
│           ├── message
│           │   └── index.html
│           └── setting
│               └── index.html
│
├── config                        # 配置目录
│   ├── app.php                   # 应用主配置
│   ├── database.php              # 数据库连接配置
│   ├── redis.php                 # Redis 连接配置
│   ├── server.php                # Webman 服务配置
│   └── third_party.php           # 存放所有第三方服务的固定配置（如 API Key）
│
├── public                        # Web 根目录，存放静态资源（CSS, JS, Images）
└── runtime                       # 运行时目录，存放日志等
```

## 6. 数据库文件 (SQL)

sql复制

```sql
-- -------------------------------------------------------------
-- Database: `your_notification_platform_db`
-- Generation Time: 2025-07-08
-- -------------------------------------------------------------

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for wm_wechat_users
-- ----------------------------
DROP TABLE IF EXISTS `wm_wechat_users`;
CREATE TABLE `wm_wechat_users`
(
    `id`         int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增 ID，平台内部唯一用户标识',
    `unionid`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '核心字段，微信开放平台下的唯一身份',
    `nickname`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户统一的昵称',
    `avatar`     varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户统一的头像 URL',
    `gender`     tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '性别 (0:未知, 1:男, 2:女)',
    `status`     tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户状态 (1: 正常, 0: 禁用)',
    `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_unionid`(`unionid`) USING BTREE,
    INDEX        `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wm_wechat_identities
-- ----------------------------
DROP TABLE IF EXISTS `wm_wechat_identities`;
CREATE TABLE `wm_wechat_identities`
(
    `id`         int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `unionid`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联到 wm_wechat_users.unionid',
    `app_id`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属应用的 AppID',
    `openid`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户在该 AppID 下的唯一标识',
    `name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属应用名称',
    `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_app_openid`(`app_id`, `openid`) USING BTREE,
    INDEX        `idx_unionid`(`unionid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户身份表（一对多）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wm_message_logs
-- ----------------------------
DROP TABLE IF EXISTS `wm_message_logs`;
CREATE TABLE `wm_message_logs`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `unionid`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接收消息的用户 UnionID (关联 wm_wechat_users.unionid)',
    `type`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '消息类型 (wechat, sms)',
    `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用的模板 ID',
    `content`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发送内容的快照 (JSON 格式)',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送状态 (0:待发送, 1:成功, 2:失败)',
    `response`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务商返回的完整响应信息',
    `created_at`  datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_unionid`(`unionid`) USING BTREE,
    INDEX         `idx_type`(`type`) USING BTREE,
    INDEX         `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息发送日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wm_files
-- ----------------------------
DROP TABLE IF EXISTS `wm_files`;
CREATE TABLE `wm_files`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `unionid`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传者用户 UnionID (关联 wm_wechat_users.unionid)',
    `file_hash`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '文件哈希值 (MD5/SHA1)，用于秒传检查',
    `file_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '原始文件名',
    `object_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '在S3中的对象名 (路径+文件名)',
    `bucket_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT 'S3 Bucket 名称',
    `url`         varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件的可访问 URL',
    `size`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小 (Bytes)',
    `mime_type`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '文件 MIME 类型',
    `created_at`  datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at`  datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_file_hash`(`file_hash`) USING BTREE COMMENT '哈希值唯一索引，秒传核心',
    INDEX         `idx_unionid`(`unionid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wm_settings
-- ----------------------------
DROP TABLE IF EXISTS `wm_settings`;
CREATE TABLE `wm_settings`
(
    `key`        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
    `value`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值',
    `name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置项名称 (用于后台显示)',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;
```

